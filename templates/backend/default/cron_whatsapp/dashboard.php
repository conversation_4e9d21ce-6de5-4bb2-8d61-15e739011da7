<?php get_template('header');?>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        .header {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        .header .subtitle {
            color: #666;
            font-size: 1.2em;
        }
        .date-info {
            background: #007bff;
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            display: inline-block;
            margin-top: 15px;
            font-weight: bold;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #007bff, #0056b3);
        }
        .stat-card.success::before {
            background: linear-gradient(90deg, #28a745, #1e7e34);
        }
        .stat-card.warning::before {
            background: linear-gradient(90deg, #ffc107, #e0a800);
        }
        .stat-card.info::before {
            background: linear-gradient(90deg, #17a2b8, #138496);
        }
        .stat-number {
            font-size: 3em;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .stat-label {
            font-size: 1.1em;
            color: #666;
            font-weight: 500;
        }
        .stat-icon {
            font-size: 2em;
            margin-bottom: 15px;
        }
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        .section {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .section-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 20px 30px;
            font-size: 1.3em;
            font-weight: bold;
        }
        .section-content {
            padding: 30px;
        }
        .summary-table {
            width: 100%;
            border-collapse: collapse;
        }
        .summary-table th,
        .summary-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        .summary-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        .summary-table tr:hover {
            background: #f8f9fa;
        }
        .progress-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 8px;
            overflow: hidden;
            margin-top: 5px;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            border-radius: 10px;
            transition: width 0.3s ease;
        }
        .actions {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }
        .btn {
            display: inline-block;
            padding: 15px 30px;
            margin: 10px;
            text-decoration: none;
            border-radius: 50px;
            font-weight: bold;
            font-size: 1.1em;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
        }
        .btn-success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
            color: white;
        }
        .btn-info {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
        }
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #545b62);
            color: white;
        }
        .no-data {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
        .refresh-info {
            text-align: center;
            margin-top: 20px;
            color: white;
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
        }
        @media (max-width: 768px) {
            .content-grid {
                grid-template-columns: 1fr;
            }
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Dashboard Cron WhatsApp</h1>
            <p class="subtitle">Sistem Reminder H-1 Perjanjian Rumah Sakit</p>
            <div class="date-info">
                📅 Tanggal Perjanjian: <?= $tanggal_format ?>
            </div>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">👥</div>
                <div class="stat-number"><?= $statistik_umum->total_perjanjian ?></div>
                <div class="stat-label">Total Perjanjian</div>
            </div>
            <div class="stat-card success">
                <div class="stat-icon">📱</div>
                <div class="stat-number"><?= $statistik_umum->dengan_nomor ?></div>
                <div class="stat-label">Dengan Nomor HP</div>
            </div>
            <div class="stat-card warning">
                <div class="stat-icon">❌</div>
                <div class="stat-number"><?= $statistik_umum->tanpa_nomor ?></div>
                <div class="stat-label">Tanpa Nomor HP</div>
            </div>
            <div class="stat-card info">
                <div class="stat-icon">👨‍⚕️</div>
                <div class="stat-number"><?= $statistik_umum->total_dokter ?></div>
                <div class="stat-label">Dokter Terlibat</div>
            </div>
        </div>

        <div class="content-grid">
            <div class="section">
                <div class="section-header">
                    👨‍⚕️ Ringkasan per Dokter
                </div>
                <div class="section-content">
                    <?php if (empty($ringkasan_dokter)): ?>
                        <div class="no-data">
                            <p>Tidak ada data dokter untuk tanggal ini</p>
                        </div>
                    <?php else: ?>
                        <table class="summary-table">
                            <thead>
                                <tr>
                                    <th>Dokter</th>
                                    <th>Total</th>
                                    <th>Dengan HP</th>
                                    <th>Progress</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($ringkasan_dokter as $dokter): ?>
                                    <?php 
                                    $percentage = $dokter->total_perjanjian > 0 ? 
                                        round(($dokter->dengan_nomor / $dokter->total_perjanjian) * 100) : 0;
                                    ?>
                                    <tr>
                                        <td><strong><?= htmlspecialchars($dokter->NAMA_DOKTER) ?></strong></td>
                                        <td><?= $dokter->total_perjanjian ?></td>
                                        <td><?= $dokter->dengan_nomor ?></td>
                                        <td>
                                            <div><?= $percentage ?>%</div>
                                            <div class="progress-bar">
                                                <div class="progress-fill" style="width: <?= $percentage ?>%"></div>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>
            </div>

            <div class="section">
                <div class="section-header">
                    🏥 Ringkasan per Ruangan
                </div>
                <div class="section-content">
                    <?php if (empty($ringkasan_ruangan)): ?>
                        <div class="no-data">
                            <p>Tidak ada data ruangan untuk tanggal ini</p>
                        </div>
                    <?php else: ?>
                        <table class="summary-table">
                            <thead>
                                <tr>
                                    <th>Ruangan</th>
                                    <th>Total</th>
                                    <th>Dengan HP</th>
                                    <th>Progress</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($ringkasan_ruangan as $ruangan): ?>
                                    <?php 
                                    $percentage = $ruangan->total_perjanjian > 0 ? 
                                        round(($ruangan->dengan_nomor / $ruangan->total_perjanjian) * 100) : 0;
                                    ?>
                                    <tr>
                                        <td><strong><?= htmlspecialchars($ruangan->NAMA_RUANGAN) ?></strong></td>
                                        <td><?= $ruangan->total_perjanjian ?></td>
                                        <td><?= $ruangan->dengan_nomor ?></td>
                                        <td>
                                            <div><?= $percentage ?>%</div>
                                            <div class="progress-bar">
                                                <div class="progress-fill" style="width: <?= $percentage ?>%"></div>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="actions">
            <h3 style="margin-bottom: 20px; color: #333;">🎯 Aksi Cepat</h3>
            
            <?php if ($statistik_umum->dengan_nomor > 0): ?>
                <a href="<?= base_url('cron_whatsapp/kirim_reminder_h1_all') ?>?tanggal=<?= $tanggal ?>&token=perjanjian_cron_2024" 
                   class="btn btn-success" 
                   onclick="return confirm('Kirim reminder WhatsApp ke <?= $statistik_umum->dengan_nomor ?> pasien?')">
                    📱 Kirim Reminder Semua
                </a>
            <?php endif; ?>
            
            <a href="<?= base_url('cron_whatsapp/view_pasien_h1') ?>?tanggal=<?= $tanggal ?>&format=html&token=perjanjian_cron_2024" 
               class="btn btn-primary">
                👥 Lihat Detail Pasien
            </a>
            
            <a href="<?= base_url('cron_whatsapp/status') ?>?tanggal=<?= $tanggal ?>&token=perjanjian_cron_2024" 
               class="btn btn-info">
                📊 Lihat Status Log
            </a>
            
            <a href="<?= current_url() ?>?<?= http_build_query($_GET) ?>" class="btn btn-secondary">
                🔄 Refresh Data
            </a>
        </div>

        <div class="refresh-info">
            <p>📅 Data diambil pada: <?= date('d/m/Y H:i:s') ?></p>
            <p>🔄 Halaman akan refresh otomatis setiap 5 menit</p>
        </div>
    </div>

    <script>
        // Auto refresh setiap 5 menit
        setTimeout(function() {
            location.reload();
        }, 300000);

        // Animasi progress bar saat load
        window.addEventListener('load', function() {
            const progressBars = document.querySelectorAll('.progress-fill');
            progressBars.forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = width;
                }, 500);
            });
        });
    </script>

<?php get_template('footer');?>
