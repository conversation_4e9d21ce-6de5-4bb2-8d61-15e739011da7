<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Konfigurasi Cron WhatsApp
 * 
 * File ini berisi konfigurasi untuk sistem cron WhatsApp reminder.
 * Sesu<PERSON>kan nilai-nilai di bawah ini dengan kebutuhan rumah sakit Anda.
 */

class Cron_whatsapp_config {
    
    /**
     * Token keamanan untuk akses cron
     * PENTING: Ganti token ini untuk production!
     */
    public static $cron_token = 'perjanjian_cron_2024';
    public static $setup_token = 'setup_cron_perjanjian_2024';
    
    /**
     * Pesan default untuk reminder
     */
    public static $default_messages = [
        'reminder_h1' => 'Reminder: Anda memiliki perjanjian besok. Mohon datang tepat waktu.',
        'operasi' => 'Reminder Operasi: Mohon puasa mulai jam 24.00 malam ini. Datang 2 jam sebelum jadwal operasi.',
        'konsultasi' => 'Reminder Konsultasi: Jangan lupa perjanjian besok. Bawa kartu identitas dan kartu BPJS.',
        'pemeriksaan' => 'Reminder Pemeriksaan: Bawa hasil laboratorium sebelumnya jika ada.',
        'kontrol' => 'Reminder Kontrol: Jangan lupa kontrol besok. Bawa obat yang sedang dikonsumsi.',
        'batal_dokter' => 'Mohon maaf, dokter tidak dapat praktek. Silakan daftar ulang atau hubungi rumah sakit.'
    ];
    
    /**
     * Konfigurasi delay antar pengiriman (dalam mikrodetik)
     * 500000 = 0.5 detik
     */
    public static $send_delay = 500000;
    
    /**
     * Konfigurasi timeout untuk curl (dalam detik)
     */
    public static $curl_timeout = 30;
    
    /**
     * Konfigurasi logging
     */
    public static $log_config = [
        'enable_detailed_log' => true,
        'log_response' => true,
        'log_retention_days' => 30,
        'max_log_size_mb' => 100
    ];
    
    /**
     * Konfigurasi jadwal default
     */
    public static $schedule_config = [
        'default_reminder_time' => '18:00',
        'operasi_reminder_time' => '15:00',
        'weekend_enabled' => false,
        'holiday_enabled' => false
    ];
    
    /**
     * Konfigurasi filter
     */
    public static $filter_config = [
        'exclude_empty_phone' => true,
        'exclude_invalid_phone' => true,
        'min_phone_length' => 10,
        'max_phone_length' => 15
    ];
    
    /**
     * Konfigurasi pesan berdasarkan rencana
     */
    public static $rencana_messages = [
        1 => 'operasi',      // ID rencana operasi
        2 => 'konsultasi',   // ID rencana konsultasi
        3 => 'pemeriksaan',  // ID rencana pemeriksaan
        4 => 'kontrol'       // ID rencana kontrol
    ];
    
    /**
     * Konfigurasi pesan berdasarkan ruangan
     */
    public static $ruangan_messages = [
        // Format: ID_RUANGAN => 'message_key'
        // Contoh:
        // 101 => 'konsultasi',
        // 102 => 'pemeriksaan',
        // 201 => 'operasi'
    ];
    
    /**
     * Konfigurasi pesan berdasarkan dokter
     */
    public static $dokter_messages = [
        // Format: ID_DOKTER => 'message_key'
        // Contoh:
        // 123 => 'operasi',
        // 456 => 'konsultasi'
    ];
    
    /**
     * Konfigurasi monitoring
     */
    public static $monitoring_config = [
        'enable_health_check' => true,
        'health_check_interval' => 6, // jam
        'alert_on_failure' => true,
        'max_failure_count' => 3
    ];
    
    /**
     * Konfigurasi backup
     */
    public static $backup_config = [
        'enable_auto_backup' => true,
        'backup_interval' => 'monthly',
        'backup_path' => '/backup/cron_whatsapp/',
        'compress_backup' => true
    ];
    
    /**
     * Get pesan berdasarkan konteks
     */
    public static function get_message($context = 'reminder_h1', $rencana_id = null, $ruangan_id = null, $dokter_id = null) {
        // Prioritas: dokter > ruangan > rencana > default
        
        if ($dokter_id && isset(self::$dokter_messages[$dokter_id])) {
            $message_key = self::$dokter_messages[$dokter_id];
            return self::$default_messages[$message_key] ?? self::$default_messages[$context];
        }
        
        if ($ruangan_id && isset(self::$ruangan_messages[$ruangan_id])) {
            $message_key = self::$ruangan_messages[$ruangan_id];
            return self::$default_messages[$message_key] ?? self::$default_messages[$context];
        }
        
        if ($rencana_id && isset(self::$rencana_messages[$rencana_id])) {
            $message_key = self::$rencana_messages[$rencana_id];
            return self::$default_messages[$message_key] ?? self::$default_messages[$context];
        }
        
        return self::$default_messages[$context] ?? self::$default_messages['reminder_h1'];
    }
    
    /**
     * Validasi nomor telepon
     */
    public static function validate_phone($phone) {
        if (!self::$filter_config['exclude_invalid_phone']) {
            return true;
        }
        
        $clean_phone = preg_replace('/[^0-9]/', '', $phone);
        $length = strlen($clean_phone);
        
        return $length >= self::$filter_config['min_phone_length'] && 
               $length <= self::$filter_config['max_phone_length'];
    }
    
    /**
     * Format nomor telepon untuk WhatsApp
     */
    public static function format_phone($phone) {
        $clean_phone = preg_replace('/[^0-9]/', '', $phone);
        
        // Jika dimulai dengan 0, ganti dengan 62
        if (substr($clean_phone, 0, 1) === '0') {
            $clean_phone = '62' . substr($clean_phone, 1);
        }
        
        // Jika belum ada +, tambahkan
        if (substr($clean_phone, 0, 1) !== '+') {
            $clean_phone = '+' . $clean_phone;
        }
        
        return $clean_phone;
    }
    
    /**
     * Cek apakah hari ini adalah hari kerja
     */
    public static function is_workday($date = null) {
        if (!$date) {
            $date = date('Y-m-d');
        }
        
        $day_of_week = date('N', strtotime($date)); // 1 = Monday, 7 = Sunday
        
        // Senin-Jumat = hari kerja
        return $day_of_week >= 1 && $day_of_week <= 5;
    }
    
    /**
     * Cek apakah tanggal adalah hari libur
     * TODO: Implementasi sesuai kalender libur nasional
     */
    public static function is_holiday($date = null) {
        if (!$date) {
            $date = date('Y-m-d');
        }
        
        // Daftar hari libur nasional (contoh)
        $holidays = [
            '2024-01-01', // Tahun Baru
            '2024-02-10', // Imlek
            '2024-03-11', // Isra Miraj
            '2024-03-29', // Wafat Isa Almasih
            '2024-04-10', // Idul Fitri
            '2024-04-11', // Idul Fitri
            '2024-05-01', // Hari Buruh
            '2024-05-09', // Kenaikan Isa Almasih
            '2024-05-20', // Hari Raya Waisak
            '2024-06-01', // Hari Lahir Pancasila
            '2024-06-17', // Idul Adha
            '2024-07-07', // Tahun Baru Islam
            '2024-08-17', // Kemerdekaan RI
            '2024-09-16', // Maulid Nabi
            '2024-12-25', // Natal
        ];
        
        return in_array($date, $holidays);
    }
    
    /**
     * Cek apakah boleh mengirim reminder pada tanggal tertentu
     */
    public static function can_send_reminder($date = null) {
        if (!$date) {
            $date = date('Y-m-d');
        }
        
        // Cek weekend
        if (!self::$schedule_config['weekend_enabled'] && !self::is_workday($date)) {
            return false;
        }
        
        // Cek holiday
        if (!self::$schedule_config['holiday_enabled'] && self::is_holiday($date)) {
            return false;
        }
        
        return true;
    }
}
