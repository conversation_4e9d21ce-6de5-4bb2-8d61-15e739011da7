<?php
/**
 * Con<PERSON>h Konfigurasi Pesan Custom untuk Cron WhatsApp
 * 
 * File ini berisi contoh-contoh pesan yang dapat disesuaikan
 * berdasarkan kebutuhan rumah sakit Anda.
 */

// ============================================================
// TEMPLATE PESAN BERDASARKAN JENIS PERJANJIAN
// ============================================================

$pesan_templates = [
    
    // Pesan untuk Operasi
    'operasi' => [
        'default' => 'Reminder Operasi: Besok Anda dijadwalkan operasi. Mohon puasa mulai jam 24.00 malam ini. Datang 2 jam sebelum jadwal operasi. Bawa kartu identitas dan kartu BPJS.',
        'bedah_umum' => 'Reminder Operasi Bedah: Besok operasi bedah Anda. Puasa total mulai jam 24.00. Datang jam 06.00 untuk persiapan. Bawa hasil lab terbaru.',
        'bedah_jantung' => 'Reminder Operasi Jantung: Besok operasi jantung Anda. Puasa total mulai jam 22.00. Datang jam 05.00. Bawa semua obat jantung yang sedang dikonsumsi.',
        'bedah_saraf' => 'Reminder Operasi Saraf: Besok operasi saraf Anda. Puasa total mulai jam 24.00. Datang jam 06.00. Hindari obat pengencer darah 3 hari sebelumnya.'
    ],
    
    // Pesan untuk Konsultasi
    'konsultasi' => [
        'default' => 'Reminder Konsultasi: Besok Anda memiliki jadwal konsultasi. Jangan lupa datang tepat waktu. Bawa kartu identitas dan kartu BPJS.',
        'spesialis_dalam' => 'Reminder Konsultasi Penyakit Dalam: Besok konsultasi dengan dokter spesialis dalam. Bawa hasil lab dan rekam medis sebelumnya.',
        'spesialis_anak' => 'Reminder Konsultasi Anak: Besok konsultasi untuk anak Anda. Bawa buku KIA, kartu vaksinasi, dan riwayat kesehatan anak.',
        'spesialis_kandungan' => 'Reminder Konsultasi Kandungan: Besok konsultasi kandungan Anda. Bawa hasil USG terakhir dan buku KIA jika ada.',
        'spesialis_jantung' => 'Reminder Konsultasi Jantung: Besok konsultasi jantung Anda. Bawa hasil EKG, echo, dan daftar obat yang sedang dikonsumsi.'
    ],
    
    // Pesan untuk Pemeriksaan
    'pemeriksaan' => [
        'default' => 'Reminder Pemeriksaan: Besok Anda dijadwalkan pemeriksaan. Bawa hasil laboratorium sebelumnya jika ada.',
        'lab_puasa' => 'Reminder Pemeriksaan Lab: Besok pemeriksaan lab puasa Anda. Puasa 10-12 jam sebelum pemeriksaan. Boleh minum air putih.',
        'lab_rutin' => 'Reminder Pemeriksaan Lab: Besok pemeriksaan lab rutin Anda. Tidak perlu puasa. Datang sesuai jadwal yang ditentukan.',
        'radiologi' => 'Reminder Pemeriksaan Radiologi: Besok pemeriksaan radiologi Anda. Lepas semua perhiasan logam. Bawa hasil pemeriksaan sebelumnya.',
        'usg' => 'Reminder Pemeriksaan USG: Besok pemeriksaan USG Anda. Untuk USG abdomen, puasa 6 jam. Untuk USG kandungan, minum air 1 liter 1 jam sebelumnya.',
        'endoskopi' => 'Reminder Pemeriksaan Endoskopi: Besok pemeriksaan endoskopi Anda. Puasa 8 jam sebelumnya. Bawa pendamping dewasa.'
    ],
    
    // Pesan untuk Kontrol
    'kontrol' => [
        'default' => 'Reminder Kontrol: Besok jadwal kontrol Anda. Jangan lupa bawa obat yang sedang dikonsumsi dan hasil pemeriksaan terakhir.',
        'diabetes' => 'Reminder Kontrol Diabetes: Besok kontrol diabetes Anda. Bawa buku catatan gula darah harian dan obat diabetes.',
        'hipertensi' => 'Reminder Kontrol Hipertensi: Besok kontrol tekanan darah Anda. Bawa catatan tekanan darah harian dan obat hipertensi.',
        'jantung' => 'Reminder Kontrol Jantung: Besok kontrol jantung Anda. Bawa semua obat jantung dan hasil pemeriksaan jantung terakhir.',
        'ginjal' => 'Reminder Kontrol Ginjal: Besok kontrol ginjal Anda. Bawa hasil lab fungsi ginjal terakhir dan daftar obat yang dikonsumsi.'
    ],
    
    // Pesan untuk Fisioterapi
    'fisioterapi' => [
        'default' => 'Reminder Fisioterapi: Besok sesi fisioterapi Anda. Gunakan pakaian yang nyaman untuk bergerak.',
        'stroke' => 'Reminder Fisioterapi Stroke: Besok sesi fisioterapi stroke Anda. Bawa pendamping dan gunakan pakaian yang mudah dilepas.',
        'ortopedi' => 'Reminder Fisioterapi Ortopedi: Besok fisioterapi tulang/sendi Anda. Bawa hasil rontgen terakhir dan gunakan sepatu yang nyaman.'
    ]
];

// ============================================================
// PESAN BERDASARKAN WAKTU PERJANJIAN
// ============================================================

$pesan_waktu = [
    'pagi' => [
        'prefix' => 'Selamat pagi! ',
        'suffix' => ' Semoga hari Anda menyenangkan.'
    ],
    'siang' => [
        'prefix' => 'Selamat siang! ',
        'suffix' => ' Terima kasih atas perhatian Anda.'
    ],
    'sore' => [
        'prefix' => 'Selamat sore! ',
        'suffix' => ' Semoga Anda dalam keadaan sehat.'
    ]
];

// ============================================================
// PESAN KHUSUS BERDASARKAN HARI
// ============================================================

$pesan_hari = [
    'senin' => 'Semangat memulai minggu! ',
    'jumat' => 'Semoga akhir pekan yang berkah! ',
    'sabtu' => 'Selamat akhir pekan! ',
    'minggu' => 'Selamat hari Minggu! '
];

// ============================================================
// PESAN EMERGENCY/URGENT
// ============================================================

$pesan_urgent = [
    'operasi_emergency' => '🚨 URGENT: Operasi emergency Anda dijadwalkan besok. Segera puasa total dan datang 2 jam lebih awal. Hubungi rumah sakit jika ada pertanyaan.',
    'konsultasi_urgent' => '⚠️ PENTING: Konsultasi urgent Anda besok. Mohon datang tepat waktu. Bawa semua hasil pemeriksaan terbaru.',
    'batal_mendadak' => '❌ PEMBERITAHUAN: Maaf, jadwal Anda besok dibatalkan mendadak karena dokter berhalangan. Silakan hubungi rumah sakit untuk reschedule.'
];

// ============================================================
// PESAN BERDASARKAN UMUR PASIEN
// ============================================================

$pesan_umur = [
    'anak' => [
        'prefix' => 'Untuk orang tua/wali: ',
        'suffix' => ' Pastikan anak dalam kondisi sehat dan tidak demam.'
    ],
    'lansia' => [
        'prefix' => 'Untuk pasien lansia: ',
        'suffix' => ' Mohon datang dengan pendamping dan bawa semua obat rutin.'
    ],
    'dewasa' => [
        'prefix' => '',
        'suffix' => ' Terima kasih atas perhatian Anda.'
    ]
];

// ============================================================
// PESAN BERDASARKAN STATUS BPJS
// ============================================================

$pesan_bpjs = [
    'bpjs' => 'Jangan lupa bawa kartu BPJS dan kartu identitas. ',
    'umum' => 'Siapkan pembayaran dan kartu identitas. ',
    'asuransi' => 'Bawa kartu asuransi dan kartu identitas. '
];

// ============================================================
// FUNGSI HELPER UNTUK GENERATE PESAN
// ============================================================

/**
 * Generate pesan berdasarkan konteks
 */
function generate_pesan_custom($jenis, $subjenis = 'default', $waktu = null, $hari = null, $umur = 'dewasa', $pembayaran = 'bpjs') {
    global $pesan_templates, $pesan_waktu, $pesan_hari, $pesan_umur, $pesan_bpjs;
    
    $pesan = '';
    
    // Tambah greeting berdasarkan waktu
    if ($waktu && isset($pesan_waktu[$waktu])) {
        $pesan .= $pesan_waktu[$waktu]['prefix'];
    }
    
    // Tambah greeting berdasarkan hari
    if ($hari && isset($pesan_hari[$hari])) {
        $pesan .= $pesan_hari[$hari];
    }
    
    // Tambah prefix berdasarkan umur
    if (isset($pesan_umur[$umur])) {
        $pesan .= $pesan_umur[$umur]['prefix'];
    }
    
    // Pesan utama
    if (isset($pesan_templates[$jenis][$subjenis])) {
        $pesan .= $pesan_templates[$jenis][$subjenis];
    } else {
        $pesan .= $pesan_templates[$jenis]['default'] ?? 'Reminder: Anda memiliki perjanjian besok.';
    }
    
    // Tambah info pembayaran
    if (isset($pesan_bpjs[$pembayaran])) {
        $pesan .= ' ' . $pesan_bpjs[$pembayaran];
    }
    
    // Tambah suffix berdasarkan umur
    if (isset($pesan_umur[$umur])) {
        $pesan .= $pesan_umur[$umur]['suffix'];
    }
    
    // Tambah suffix berdasarkan waktu
    if ($waktu && isset($pesan_waktu[$waktu])) {
        $pesan .= $pesan_waktu[$waktu]['suffix'];
    }
    
    return trim($pesan);
}

// ============================================================
// CONTOH PENGGUNAAN
// ============================================================

/*
// Contoh 1: Pesan operasi bedah umum pagi hari
$pesan1 = generate_pesan_custom('operasi', 'bedah_umum', 'pagi', 'senin', 'dewasa', 'bpjs');

// Contoh 2: Pesan konsultasi anak sore hari
$pesan2 = generate_pesan_custom('konsultasi', 'spesialis_anak', 'sore', null, 'anak', 'bpjs');

// Contoh 3: Pesan pemeriksaan lab puasa
$pesan3 = generate_pesan_custom('pemeriksaan', 'lab_puasa', 'pagi', null, 'dewasa', 'umum');

// Contoh 4: Pesan kontrol diabetes untuk lansia
$pesan4 = generate_pesan_custom('kontrol', 'diabetes', 'siang', 'jumat', 'lansia', 'bpjs');

echo $pesan1 . "\n";
echo $pesan2 . "\n";
echo $pesan3 . "\n";
echo $pesan4 . "\n";
*/

// ============================================================
// MAPPING UNTUK INTEGRASI DENGAN SISTEM
// ============================================================

/**
 * Mapping ID rencana ke jenis dan subjenis pesan
 */
$mapping_rencana = [
    1 => ['jenis' => 'operasi', 'subjenis' => 'default'],
    2 => ['jenis' => 'konsultasi', 'subjenis' => 'default'],
    3 => ['jenis' => 'pemeriksaan', 'subjenis' => 'default'],
    4 => ['jenis' => 'kontrol', 'subjenis' => 'default'],
    5 => ['jenis' => 'fisioterapi', 'subjenis' => 'default'],
    // Tambahkan mapping sesuai ID rencana di database Anda
];

/**
 * Mapping ID dokter ke subjenis pesan
 */
$mapping_dokter = [
    // Contoh: ID dokter spesialis dalam
    101 => ['subjenis' => 'spesialis_dalam'],
    102 => ['subjenis' => 'spesialis_anak'],
    103 => ['subjenis' => 'spesialis_kandungan'],
    104 => ['subjenis' => 'spesialis_jantung'],
    // Tambahkan mapping sesuai ID dokter di database Anda
];

/**
 * Mapping ID ruangan ke subjenis pesan
 */
$mapping_ruangan = [
    // Contoh: ID ruangan operasi
    201 => ['subjenis' => 'bedah_umum'],
    202 => ['subjenis' => 'bedah_jantung'],
    203 => ['subjenis' => 'bedah_saraf'],
    // Lab
    301 => ['subjenis' => 'lab_puasa'],
    302 => ['subjenis' => 'lab_rutin'],
    // Radiologi
    401 => ['subjenis' => 'radiologi'],
    402 => ['subjenis' => 'usg'],
    // Tambahkan mapping sesuai ID ruangan di database Anda
];

?>
