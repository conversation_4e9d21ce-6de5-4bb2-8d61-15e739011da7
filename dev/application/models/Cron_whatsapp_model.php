<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Cron_whatsapp_model extends CI_Model {

    function __construct() {
        parent::__construct();
    }

    /**
     * Ambil data perjanjian H-1 berdasarkan parameter
     * 
     * @param array $params Parameter filter (dokter, r<PERSON><PERSON>, tanggal, renc<PERSON>)
     * @return array Data perjanjian
     */
    public function get_perjanjian_h1($params = []) {
        $this->db->select('
            rp.ID, 
            rp.NOMR, 
            rp.NAMAPASIEN, 
            rp.NOMOR, 
            rp.TANGGAL,
            DATE_FORMAT(rp.TANGGAL, "%d/%m/%Y") as TANGGAL_FORMAT,
            `master`.getNamaLengkapPegawai(md.NIP) as DOKTER, 
            mr.DESKRIPSI as RUANGAN,
            rr.DESKRIPSI as RENCANA,
            rp.STATUS_SORE, 
            STR_TO_DATE(IF(ISNULL(rjd.AWAL), `rj`.`AWAL`, rjd.AWAL), "%H:%i") AS AWAL, 
            STR_TO_DATE(IF(ISNULL(rjd.AKHIR), `rj`.`AKHIR`, rjd.AKHIR), "%H:%i") AS AKHIR,
            rj.AWAL_SORE,
            rj.AKHIR_SORE
        ');
        
        $this->db->from('remun_medis.perjanjian rp');
        $this->db->join('`master`.dokter md', 'rp.ID_DOKTER = md.ID');
        $this->db->join('`master`.ruangan mr', 'rp.ID_RUANGAN = mr.ID');
        $this->db->join('remun_medis.rencana rr', 'rp.RENCANA = rr.ID', 'LEFT');
        $this->db->join('remun_medis.jadwal rj', 'rp.ID_DOKTER = rj.DOKTER AND rp.ID_RUANGAN = rj.RUANGAN AND rp.TANGGAL = rj.TANGGAL', 'LEFT');
        $this->db->join('remun_medis.jadwal_detail rjd', 'rp.SLOT = rjd.ID', 'LEFT');
        // Filter aktif
        $this->db->where('rp.STATUS !=', 0);
        $this->db->where('rp.DPJP !=', 0);
        
        // Filter berdasarkan parameter
        if (!empty($params['dokter'])) {
            $this->db->where('rp.ID_DOKTER', $params['dokter']);
        }
        
        if (!empty($params['ruangan'])) {
            $this->db->where('rp.ID_RUANGAN', $params['ruangan']);
        }
        
        if (!empty($params['tanggal'])) {
            $this->db->where('rp.TANGGAL', $params['tanggal']);
        }
        
        if (!empty($params['rencana'])) {
            $this->db->where('rp.RENCANA', $params['rencana']);
        }
        
        // Filter nomor telepon tidak kosong
        $this->db->where('rp.NOMOR IS NOT NULL');
        $this->db->where('rp.NOMOR !=', '');
        $this->db->where('mr.GEDUNG IS NULL');
        $this->db->where_not_in('rp.ID_RUANGAN', ['105060101','105120101','105110101','105020101','105020901','105020102','105090101','105020401','105070101']);
        
        $this->db->order_by('rp.TANGGAL, md.NIP, mr.DESKRIPSI, rp.NAMAPASIEN');
        // $this->db->limit(5);
        $query = $this->db->get();
        return $query->result();
    }

    /**
     * Log aktivitas cron
     * 
     * @param array $data Data log
     * @return int ID log yang dibuat
     */
    public function log_cron_activity($data) {
        $log_data = [
            'type' => $data['type'],
            'parameters' => $data['parameters'],
            'total_perjanjian' => $data['total_perjanjian'],
            'pesan' => $data['pesan'],
            'tanggal_eksekusi' => date('Y-m-d H:i:s'),
            'status' => 'running',
            'berhasil' => 0,
            'gagal' => 0
        ];
        
        $this->db->insert('remun_medis.log_cron_whatsapp', $log_data);
        return $this->db->insert_id();
    }

    /**
     * Update log cron dengan hasil akhir
     * 
     * @param int $log_id ID log
     * @param array $data Data update
     */
    public function update_log_cron($log_id, $data) {
        $update_data = $data;
        $update_data['tanggal_selesai'] = date('Y-m-d H:i:s');
        
        $this->db->where('id', $log_id);
        $this->db->update('remun_medis.log_cron_whatsapp', $update_data);
    }

    /**
     * Log detail pengiriman per pasien
     * 
     * @param array $data Data detail pengiriman
     */
    public function log_detail_pengiriman($data) {
        $detail_data = [
            'id_log_cron' => $data['id_log_cron'],
            'id_perjanjian' => $data['id_perjanjian'],
            'nomor' => $data['nomor'],
            'pesan' => $data['pesan'],
            'response' => $data['response'],
            'status' => $data['status'],
            'tanggal_kirim' => date('Y-m-d H:i:s')
        ];
        
        $this->db->insert('remun_medis.log_detail_cron_whatsapp', $detail_data);
    }

    /**
     * Ambil detail log berdasarkan ID
     * 
     * @param int $log_id ID log
     * @return object Data log dengan detail
     */
    public function get_log_detail($log_id) {
        // Ambil data log utama
        $this->db->select('*');
        $this->db->from('remun_medis.log_cron_whatsapp');
        $this->db->where('id', $log_id);
        $log = $this->db->get()->row();
        
        if ($log) {
            // Ambil detail pengiriman
            $this->db->select('
                ld.*,
                p.NOMR,
                p.NAMAPASIEN,
                `master`.getNamaLengkapPegawai(md.NIP) as DOKTER,
                mr.DESKRIPSI as RUANGAN
            ');
            $this->db->from('remun_medis.log_detail_cron_whatsapp ld');
            $this->db->join('remun_medis.perjanjian p', 'ld.id_perjanjian = p.ID');
            $this->db->join('`master`.dokter md', 'p.ID_DOKTER = md.ID');
            $this->db->join('`master`.ruangan mr', 'p.ID_RUANGAN = mr.ID');
            $this->db->where('ld.id_log_cron', $log_id);
            $this->db->order_by('ld.tanggal_kirim');
            
            $log->detail = $this->db->get()->result();
        }
        
        return $log;
    }

    /**
     * Ambil log berdasarkan tanggal
     * 
     * @param string $tanggal Tanggal (Y-m-d)
     * @return array Data log
     */
    public function get_log_by_date($tanggal) {
        $this->db->select('*');
        $this->db->from('remun_medis.log_cron_whatsapp');
        $this->db->where('DATE(tanggal_eksekusi)', $tanggal);
        $this->db->order_by('tanggal_eksekusi', 'DESC');
        
        return $this->db->get()->result();
    }

    /**
     * Ambil statistik pengiriman
     * 
     * @param string $start_date Tanggal mulai
     * @param string $end_date Tanggal akhir
     * @return object Statistik
     */
    public function get_statistik_pengiriman($start_date = null, $end_date = null) {
        if (!$start_date) {
            $start_date = date('Y-m-d', strtotime('-7 days'));
        }
        if (!$end_date) {
            $end_date = date('Y-m-d');
        }
        
        $this->db->select('
            COUNT(*) as total_eksekusi,
            SUM(total_perjanjian) as total_perjanjian,
            SUM(berhasil) as total_berhasil,
            SUM(gagal) as total_gagal,
            AVG(berhasil) as rata_berhasil,
            AVG(gagal) as rata_gagal
        ');
        $this->db->from('remun_medis.log_cron_whatsapp');
        $this->db->where('DATE(tanggal_eksekusi) >=', $start_date);
        $this->db->where('DATE(tanggal_eksekusi) <=', $end_date);
        
        return $this->db->get()->row();
    }

    /**
     * Buat tabel log jika belum ada
     */
    public function create_log_tables() {
        // Tabel log utama cron
        $sql_log_cron = "
        CREATE TABLE IF NOT EXISTS `remun_medis`.`log_cron_whatsapp` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `type` varchar(50) NOT NULL,
            `parameters` text,
            `total_perjanjian` int(11) DEFAULT 0,
            `berhasil` int(11) DEFAULT 0,
            `gagal` int(11) DEFAULT 0,
            `pesan` text,
            `tanggal_eksekusi` datetime NOT NULL,
            `tanggal_selesai` datetime NULL,
            `status` enum('running','completed','error') DEFAULT 'running',
            PRIMARY KEY (`id`),
            KEY `idx_tanggal_eksekusi` (`tanggal_eksekusi`),
            KEY `idx_type` (`type`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8;
        ";
        
        // Tabel detail pengiriman
        $sql_log_detail = "
        CREATE TABLE IF NOT EXISTS `remun_medis`.`log_detail_cron_whatsapp` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `id_log_cron` int(11) NOT NULL,
            `id_perjanjian` int(11) NOT NULL,
            `nomor` varchar(20) NOT NULL,
            `pesan` text,
            `response` text,
            `status` varchar(20) DEFAULT 'unknown',
            `tanggal_kirim` datetime NOT NULL,
            PRIMARY KEY (`id`),
            KEY `idx_log_cron` (`id_log_cron`),
            KEY `idx_perjanjian` (`id_perjanjian`),
            KEY `idx_tanggal_kirim` (`tanggal_kirim`),
            FOREIGN KEY (`id_log_cron`) REFERENCES `log_cron_whatsapp`(`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8;
        ";
        
        $this->db->query($sql_log_cron);
        $this->db->query($sql_log_detail);
    }

    /**
     * Ambil daftar dokter aktif
     */
    public function get_dokter_aktif() {
        $this->db->select('md.ID, `master`.getNamaLengkapPegawai(md.NIP) as NAMA');
        $this->db->from('`master`.dokter md');
        $this->db->join('`master`.pegawai mp', 'md.NIP = mp.NIP');
        $this->db->where('mp.STATUS', 1);
        $this->db->order_by('mp.NAMA');
        
        return $this->db->get()->result();
    }

    /**
     * Ambil daftar ruangan aktif
     */
    public function get_ruangan_aktif() {
        $this->db->select('ID, DESKRIPSI');
        $this->db->from('`master`.ruangan');
        $this->db->where('STATUS', 1);
        $this->db->order_by('DESKRIPSI');
        
        return $this->db->get()->result();
    }

    /**
     * Ambil daftar rencana
     */
    public function get_rencana() {
        $this->db->select('ID, DESKRIPSI');
        $this->db->from('remun_medis.rencana');
        $this->db->where('STATUS', 1);
        $this->db->order_by('DESKRIPSI');

        return $this->db->get()->result();
    }

    /**
     * Ambil statistik detail perjanjian H-1
     *
     * @param array $params Parameter filter
     * @return object Statistik detail
     */
    public function get_statistik_perjanjian_h1($params = []) {
        $this->db->select('
            COUNT(*) as total_perjanjian,
            COUNT(CASE WHEN rp.NOMOR IS NOT NULL AND rp.NOMOR != "" THEN 1 END) as dengan_nomor,
            COUNT(CASE WHEN rp.NOMOR IS NULL OR rp.NOMOR = "" THEN 1 END) as tanpa_nomor,
            COUNT(DISTINCT rp.ID_DOKTER) as total_dokter,
            COUNT(DISTINCT rp.ID_RUANGAN) as total_ruangan,
            COUNT(DISTINCT rp.RENCANA) as total_rencana
        ');

        $this->db->from('remun_medis.perjanjian rp');
        $this->db->join('`master`.dokter md', 'rp.ID_DOKTER = md.ID');
        $this->db->join('`master`.ruangan mr', 'rp.ID_RUANGAN = mr.ID');
        $this->db->join('remun_medis.rencana rr', 'rp.RENCANA = rr.ID', 'LEFT');

        // Filter aktif
        $this->db->where('rp.STATUS !=', 0);

        // Filter berdasarkan parameter
        if (!empty($params['dokter'])) {
            $this->db->where('rp.ID_DOKTER', $params['dokter']);
        }

        if (!empty($params['ruangan'])) {
            $this->db->where('rp.ID_RUANGAN', $params['ruangan']);
        }

        if (!empty($params['tanggal'])) {
            $this->db->where('rp.TANGGAL', $params['tanggal']);
        }

        if (!empty($params['rencana'])) {
            $this->db->where('rp.RENCANA', $params['rencana']);
        }

        return $this->db->get()->row();
    }

    /**
     * Ambil ringkasan perjanjian per dokter untuk tanggal tertentu
     *
     * @param string $tanggal Tanggal (Y-m-d)
     * @return array Ringkasan per dokter
     */
    public function get_ringkasan_per_dokter($tanggal = null) {
        if (!$tanggal) {
            $tanggal = date('Y-m-d', strtotime('+1 day'));
        }

        $this->db->select('
            rp.ID_DOKTER,
            `master`.getNamaLengkapPegawai(md.NIP) as NAMA_DOKTER,
            COUNT(*) as total_perjanjian,
            COUNT(CASE WHEN rp.NOMOR IS NOT NULL AND rp.NOMOR != "" THEN 1 END) as dengan_nomor,
            COUNT(CASE WHEN rp.NOMOR IS NULL OR rp.NOMOR = "" THEN 1 END) as tanpa_nomor
        ');

        $this->db->from('remun_medis.perjanjian rp');
        $this->db->join('`master`.dokter md', 'rp.ID_DOKTER = md.ID');
        $this->db->where('rp.STATUS !=', 0);
        $this->db->where('rp.TANGGAL', $tanggal);
        $this->db->group_by('rp.ID_DOKTER, md.NIP');
        $this->db->order_by('NAMA_DOKTER');

        return $this->db->get()->result();
    }

    /**
     * Ambil ringkasan perjanjian per ruangan untuk tanggal tertentu
     *
     * @param string $tanggal Tanggal (Y-m-d)
     * @return array Ringkasan per ruangan
     */
    public function get_ringkasan_per_ruangan($tanggal = null) {
        if (!$tanggal) {
            $tanggal = date('Y-m-d', strtotime('+1 day'));
        }

        $this->db->select('
            rp.ID_RUANGAN,
            mr.DESKRIPSI as NAMA_RUANGAN,
            COUNT(*) as total_perjanjian,
            COUNT(CASE WHEN rp.NOMOR IS NOT NULL AND rp.NOMOR != "" THEN 1 END) as dengan_nomor,
            COUNT(CASE WHEN rp.NOMOR IS NULL OR rp.NOMOR = "" THEN 1 END) as tanpa_nomor
        ');

        $this->db->from('remun_medis.perjanjian rp');
        $this->db->join('`master`.ruangan mr', 'rp.ID_RUANGAN = mr.ID');
        $this->db->where('rp.STATUS !=', 0);
        $this->db->where('rp.TANGGAL', $tanggal);
        $this->db->group_by('rp.ID_RUANGAN, mr.DESKRIPSI');
        $this->db->order_by('NAMA_RUANGAN');

        return $this->db->get()->result();
    }

    /**
     * Cek apakah ada perjanjian untuk tanggal tertentu
     *
     * @param string $tanggal Tanggal (Y-m-d)
     * @return bool
     */
    public function has_perjanjian($tanggal = null) {
        if (!$tanggal) {
            $tanggal = date('Y-m-d', strtotime('+1 day'));
        }

        $this->db->select('COUNT(*) as total');
        $this->db->from('remun_medis.perjanjian');
        $this->db->where('STATUS !=', 0);
        $this->db->where('TANGGAL', $tanggal);

        $result = $this->db->get()->row();
        return $result->total > 0;
    }
}
