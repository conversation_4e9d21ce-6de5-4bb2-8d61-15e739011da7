<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Perjanjian_model extends My_Model{
	protected $_table_name = 'remun_medis.perjanjian';
    protected $_primary_key = 'ID';
    protected $_order_by = 'ID';
    protected $_order_by_type = 'ASC';

    public $rules = array(
            'norm' => array(
                'field' => 'norm',
                'rules' => 'trim|required'
            ),
            'nama' => array(
                'field' => 'nama',
                'rules' => 'trim|required'
            ),
            'dokter' => array(
                'field' => 'dokter',
                'rules' => 'trim|required'
            ),
            'idr' => array(
                'field' => 'idr',
                'rules' => 'trim|required'
            ),
            'tgl' => array(
                'field' => 'rgl',
                'rules' => 'trim|required'
            ),
    );

    function __construct(){
        parent::__construct();
        // $this->db2 = $this->load->database('dummy',TRUE);
    }

    function table_query()
    {
        $this->db->select('rp.ID, rp.NOMR, rp.NAMAPASIEN ,`master`.getNama<PERSON>engkap<PERSON>egawai(md.NIP) DOKTER, mr.DE<PERSON>PSI RUANGAN, rp.TANGGAL,rp.NOMOR ,LPAD(rp.NOKONTROL,6,"0") NOKONTROL, rr.DESKRIPSI RENCANA, rp.STATUS_SORE');
        $this->db->from('remun_medis.perjanjian rp');
        $this->db->join('`master`.dokter md','rp.ID_DOKTER = md.ID');
        $this->db->join('`master`.ruangan mr','rp.ID_RUANGAN = mr.ID');
        $this->db->join('remun_medis.rencana rr','rp.RENCANA = rr.ID','LEFT');
        $this->db->where('rp.STATUS !=', 0);

        $this->db->order_by('rp.ID','DESC');
        if($this->input->post('norm'))
        {
            $this->db->where('rp.NORM', $this->input->post('norm'));
        }
        if($this->input->post('dokter'))
        {
            $this->db->where('rp.ID_DOKTER', $this->input->post('dokter'));
        }
        if($this->input->post('ruangan'))
        {
            $this->db->where('rp.ID_RUANGAN', $this->input->post('ruangan'));
        }
        if($this->input->post('awal'))
        {
            $this->db->where('rp.TANGGAL', $this->input->post('awal'));
        }else{
            $this->db->where('rp.TANGGAL >= CURDATE()');
        }
        if($this->input->post('rencana'))
        {
            $this->db->where('rp.RENCANA', $this->input->post('rencana'));
        }

        if($this->input->post('jenis') != "")
        {
            $this->db->where('rp.STATUS_SORE', $this->input->post('jenis'));
        }

    }


    function create()
    {
        $this->db->trans_begin();
        $NoKontrol = $this->NoKontrol();
        $rencana = 0;
        $tujuanOperasi = null;
        $NoKontrolDokter = null;
        $oleh = $this->session->userdata('id');
        $persetujuanInstalasi = 0;
        $prosedur = 0;
        $dpjp = 1;
        if($this->input->post('rencana')){
            $rencana = $this->input->post('rencana');
            // if($rencana == 1){
            //     $NoKontrolDokter = $this->NoKontrolDokter();
            // }
        }
        if($this->input->post('tujuanOperasi')){
            $tujuanOperasi = $this->input->post('tujuanOperasi');
        }
        if($this->input->post('persetujuanInstalasi')){
            $persetujuanInstalasi = $this->input->post('persetujuanInstalasi');
        }
        if($this->input->post('oleh')){
            $oleh = $this->input->post('oleh');
        }
        if($this->input->post('idj')){
            $prosedur = 1;
        }
        if(!$this->input->post('dpjp')){
            $dpjp = 0;
        }
        $field = array(
            'NOMR' => $this->input->post('norm'),
            'NAMAPASIEN' => $this->input->post('nama'),
            'ID_DOKTER' => $this->input->post('dokter'),
            'ID_RUANGAN' => $this->input->post('idr'),
            'TANGGAL' => $this->input->post('tgl'),
            'KETERANGAN' => '-',
            'RENCANA' => $rencana,
            'TUJUANOPERASI' => $tujuanOperasi,
            'NOMOR' => $this->input->post('nomor'),
            'TANGGALRAWATINAP' => $this->input->post('tanggalRawatInap'),
            'DIAGNOSA' => $this->input->post('diagnosa'),
            'TINDAKAN' => $this->input->post('tindakan'),
            'OLEH' => $oleh,
            'STATUS' => '1',
            'NOKONTROL' => $NoKontrol,
            'NOKONTROLDOKTER' => $NoKontrolDokter,
            'PERSETUJUAN' => $persetujuanInstalasi,
            'STATUS_SORE' => $this->input->post('statusSore'),
            'DPJP' => $dpjp,
        );
        $this->db->insert('remun_medis.perjanjian', $field);
        $id = $this->db->insert_id();
        
        if(($this->input->post('idr')==105020704 || $this->input->post('idr')==105020705 || $this->input->post('idr')==105120101 || $this->input->post('idr')==105020201 || $this->input->post('idr')==105060101) && $this->cekAntrian() == 0){
            if($this->input->post('idr')==105120101){
                $ruangan = 'E'; //Instalasi Radio Terapi
            }
            elseif($this->input->post('idr')==105020704){
                $ruangan = 'D'; //Poliklinik Onkologi 1
            }
            elseif($this->input->post('idr')==105020705){
                $ruangan = 'C'; //Poliklinik Onkologi 2
            }
            elseif($this->input->post('idr')==105020201){
                $ruangan = 'B'; //Poli Cendana
            }
            elseif($this->input->post('idr')==105060101){
                $ruangan = 'A'; //Unit Prosedur Diagnostik
            }
            $field2 = array(
                'PASIEN' => $this->input->post('norm'),
                'NAMA' => $this->input->post('nama'),
                'KONTAK' => $this->input->post('nomor'),
                'TANGGAL_KUNJUNGAN' => $this->input->post('tgl'),
                'TANGGAL_KUNJUNGAN_ANTRIAN' => $this->input->post('tgl'),
                'RUANGAN' => $ruangan,
                'DOKTER' => NULL,
                // 'OLEH' => $this->session->userdata('id'),
                'STATUS' => '1',
            );
            $this->db->insert('kemkes.reservasi_antrian', $field2);
        }

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            return false;
        } else {
            $this->db->trans_commit();
            // $id = $this->getId();
            return array('status' => 200, 'message' => 'Berhasil.', 'id' => $id, 'data' => array('status_prosedur' => $prosedur));
        }
    }

    function batalPasien(){
        $oleh = $this->session->userdata('id');
        $id = $this->input->post('id');
        if($this->input->post('oleh')){
            $oleh = $this->input->post('oleh');
        }

        if (!$id) {
            return array('status' => 400, 'message' => 'ID tidak boleh kosong.');
        }

        // Ambil data perjanjian yang akan dibatalkan
        $this->db->select('ID, NOMR, TANGGAL, DPJP');
        $this->db->from('remun_medis.perjanjian');
        $this->db->where('ID', $id);
        $perjanjian = $this->db->get()->row();

        $field = array(
            'STATUS' => '0',
            'DELETED_BY' => $oleh,
            'DPJP' => 0,
        );
        $this->db->set('DELETED_AT', 'NOW()', FALSE);
        $this->db->where('ID', $id);
        $this->db->update('remun_medis.perjanjian', $field);
        $affected = $this->db->affected_rows();

        // Jika perjanjian yang dibatalkan DPJP=1, cari perjanjian aktif lain di hari yang sama dan set DPJP=1 pada salah satunya
        if ($affected > 0 && $perjanjian && $perjanjian->DPJP == 1) {
            $this->db->select('ID');
            $this->db->from('remun_medis.perjanjian');
            $this->db->where('NOMR', $perjanjian->NOMR);
            $this->db->where('TANGGAL', $perjanjian->TANGGAL);
            $this->db->where('STATUS !=', 0);
            $this->db->where('ID !=', $perjanjian->ID);
            $this->db->limit(1);
            $aktif = $this->db->get()->row();
            if ($aktif) {
                $this->db->where('ID', $aktif->ID);
                $this->db->update('remun_medis.perjanjian', array('DPJP' => 1));
            }
        }

        if($affected > 0){
            return array('status' => 200, 'message' => 'Berhasil.');
        } else {
            return array('status' => 400, 'message' => 'Tidak ada data yang diubah.');
        }
    }

    function batalDokter(){
        $field = array(
            // 'KETERANGAN' => $this->input->post('keterangan'),
            'BATAL_OLEH' => $this->session->userdata('id'),
            'STATUS' => '0',
        );
        $this->db->where(array('rj.DOKTER' => $this->input->post('dokter'), 'rj.RUANGAN' => $this->input->post('ruangan'), 'rj.TANGGAL' => $this->input->post('tanggal')));
        $this->db->update('remun_medis.jadwal rj', $field);

        if($this->db->affected_rows() > 0){
            return array('status' => 200, 'message' => 'Berhasil.');

        }
    }

    function cek(){
        $getPenjamin = $this->getPenjamin();

        $this->db->select('rp.ID,GROUP_CONCAT(CONCAT("Perjanjian dengan ", `master`.getNamaLengkapPegawai(md.NIP), " di ruangan ", mr.DESKRIPSI) SEPARATOR " dan ") INFO, GROUP_CONCAT(rp.ID_DOKTER) ID_DOKTER');
        $this->db->from('remun_medis.perjanjian rp');
        $this->db->join('`master`.dokter md','rp.ID_DOKTER = md.ID');
        $this->db->join('`master`.ruangan mr','rp.ID_RUANGAN = mr.ID');
        if($this->input->post('norm') != '0'){
            if($getPenjamin != 2 || $this->input->post('idr') == '105090101'){
                $this->db->where(array('rp.NOMR' => $this->input->post('norm'), 'rp.TANGGAL' => $this->input->post('tgl'),'rp.ID_DOKTER' => $this->input->post('dokter'),'rp.STATUS !=' => 0));
            }else{
                $this->db->where(array('rp.NOMR' => $this->input->post('norm'), 'rp.TANGGAL' => $this->input->post('tgl'),'rp.STATUS !=' => 0));
            }
        }else{
            $this->db->where(array('rp.NOMR' => $this->input->post('norm'), 'rp.TANGGAL' => $this->input->post('tgl'),'rp.NOMOR' => $this->input->post('nomor'),'rp.STATUS !=' => 0));
        }
        $query = $this->db->get();
        $row = $query->row();
        $num = $query->num_rows();

        $cekKuota = $this->cekKuota();
        $cekKuotaSore = $this->cekKuotaSore();
        $cekPenjamin = $this->cekPenjamin();
        $cekRencana = $this->cekRencana();
        $sore = $this->input->post('sore');
        $day = date('D', strtotime($this->input->post('tgl'))); 

        if($row -> ID != NULL){
            if(($this->input->post('idr') == 105020708 || $this->input->post('idr') == 105020705) && !in_array($this->input->post('dokter'), explode(',', $row->ID_DOKTER)) && $this->input->post('oleh') != 1065){
                return array('status' => 200, 'message' => 'Sudah memiliki perjanjian di hari yang sama.', 'id' => $row -> ID,'info' => $row -> INFO);
            } else {
                return array('status' => 503, 'message' => 'Mohon maaf Anda tidak dapat melakukan perjanjian ini karena Anda sudah memiliki perjanjian konsultasi dengan dokter yang dituju.', 'id' => $row -> ID,'info' => $row -> INFO);
            }
        }elseif(($cekKuota -> STATUS == 0 && !isset($_POST['persetujuanInstalasi'])) && (($this->input->post('rencana') == 1 && $this->input->post('dokter') != 114) || ($this->input->post('dokter') == 114)) && ($this->input->post('smf') != 38 || $this->input->post('smf') != 39)){
            if($getPenjamin == 2){
                return array('status' => 406, 'message' => 'Mohon maaf Anda tidak dapat melakukan  perjanjian ini karena jadwal perjanjian yang dituju sudah penuh. Silakan pilih hari lain');
            }

            if($cekKuotaSore -> STATUS == 0){
                return array('status' => 406, 'message' => 'Mohon maaf Anda tidak dapat melakukan  perjanjian ini karena jadwal perjanjian yang dituju sudah penuh. Silakan pilih hari lain');
            }
            return array('status' => 200, 'message' => 'Success','data' => array('status_sore' => 1));
        }elseif($getPenjamin != 1 && $sore == 1){
            return array('status' => 406, 'message' => 'Mohon maaf Anda tidak dapat melakukan  perjanjian ini.');
        }elseif(($cekKuotaSore -> STATUS == 0 && !isset($_POST['persetujuanInstalasi'])) && $this->input->post('rencana') == 1 && $sore == 1){
            return array('status' => 406, 'message' => 'Mohon maaf Anda tidak dapat melakukan  perjanjian ini karena jadwal perjanjian yang dituju sudah penuh. Silakan pilih hari lain');
        }elseif($this->input->post('rencana') == 10 && $cekRencana >= 30){
            return array('status' => 406, 'message' => 'Mohon maaf Anda tidak dapat melakukan  perjanjian ini karena jadwal perjanjian yang dituju sudah penuh. Silakan pilih hari lain', 'tes' => $cekRencana);
        }elseif((($this->input->post('rencana') == 2 && $this->input->post('dokter') == 5 && $this->cekRencana(1) >= 10) || ($this->input->post('rencana') == 2 && $this->input->post('dokter') == 117 && $this->cekRencana(1) >= 5) || ($this->input->post('rencana') == 2 && $this->input->post('dokter') == 108 && $this->cekRencana(1) >= 8)) && !isset($_POST['persetujuanInstalasi'])){
            return array('status' => 406, 'message' => 'Mohon maaf Anda tidak dapat melakukan  perjanjian ini karena jadwal perjanjian yang dituju sudah penuh. Silakan pilih hari lain', 'tes' => $this->cekRencana(1));
        }elseif($this->input->post('rencana') == 1 && $this->input->post('norm') != '0'){
            if($this->input->post('dokter') == 5 && $this->cekRencana(2) >= 1){
                return array('status' => 406, 'message' => 'Mohon maaf Anda tidak dapat melakukan perjanjian ini karena Anda sudah memiliki perjanjian konsultasi dengan dokter yang dituju.', 'tes' => $this->cekRencana(2));
            }elseif($this->cekRencana(3) >= 1){
                return array('status' => 406, 'message' => 'Mohon maaf Anda tidak dapat melakukan perjanjian ini karena Anda sudah memiliki perjanjian konsultasi dengan dokter yang dituju.', 'tes' => $this->cekRencana(3));
            }
        }
        return array('status' => 200, 'message' => 'Success','data' => array('status_sore' => $sore));

    }

    function cekKuota(){
        $this->db->select('IF(COUNT(*) >= (SELECT rj.KUOTA FROM remun_medis.jadwal rj WHERE `rj`.`DOKTER` = "'.$this->input->post('dokter').'" AND `rj`.`TANGGAL` = "'.$this->input->post('tgl').'" AND `rj`.`RUANGAN` = "'.$this->input->post('idr').'" AND `rj`.STATUS != 0), 0, 1) STATUS, IF(COUNT(*) = (SELECT rj.KUOTA FROM remun_medis.jadwal rj WHERE `rj`.`DOKTER` = "'.$this->input->post('dokter').'" AND `rj`.`TANGGAL` = "'.$this->input->post('tgl').'" AND `rj`.`RUANGAN` = "'.$this->input->post('idr').'" AND `rj`.STATUS != 0) - 1, 0, 1) SISA');
        $this->db->from('remun_medis.perjanjian rp');
        $this->db->where(array('rp.ID_DOKTER' => $this->input->post('dokter'), 'rp.TANGGAL' => $this->input->post('tgl'),'rp.ID_RUANGAN' => $this->input->post('idr'),'rp.STATUS !=' => 0));
        $this->db->where('rp.STATUS_SORE = 0');
        if($this->input->post('dokter') != 114){
            $this->db->where('rp.RENCANA IN(0,1)');
        }
        // $this->db->group_by('rp.ID_DOKTER,rp.ID_RUANGAN,rp.TANGGAL');
        $query = $this->db->get();
        // $row = $query->row();

        return $query->row();
        // $num = $query->num_rows();

        // if($row -> STATUS == 1){
        //     return array('status' => 503, 'message' => 'Kuota Penuh');
        // }
        // return array('status' => 200, 'message' => 'Success');

    }

    function cekKuotaSore(){
        $this->db->select('IF(COUNT(*) >= (SELECT IF(rj.KUOTASORE IS NULL,0,rj.KUOTASORE) FROM remun_medis.jadwal rj WHERE `rj`.`DOKTER` = "'.$this->input->post('dokter').'" AND `rj`.`TANGGAL` = "'.$this->input->post('tgl').'" AND `rj`.`RUANGAN` = "'.$this->input->post('idr').'" AND `rj`.STATUS != 0), 0, 1) STATUS');
        $this->db->from('remun_medis.perjanjian rp');
        $this->db->where(array('rp.ID_DOKTER' => $this->input->post('dokter'), 'rp.TANGGAL' => $this->input->post('tgl'),'rp.ID_RUANGAN' => $this->input->post('idr'),'rp.STATUS !=' => 0));
        $this->db->where('rp.RENCANA IN(0,1) AND rp.STATUS_SORE = 1');
        $query = $this->db->get();

        return $query->row();
    }

    function cekKuotaKamar(){
        $this->db->select('IF(COUNT(*) >= (SELECT j.KUOTA FROM remun_medis.jadwal_prosedur j WHERE j.ID = '.$this->input->post('idj').'), 0, 1) STATUS');
        $this->db->from('remun_medis.perjanjian_prosedur p');
        $this->db->where(array('p.ID_JADWAL' => $this->input->post('idj'),'p.STATUS !=' => 0));
        $query = $this->db->get();

        return $query->row();
    }

    function cekJadwalKamar(){
        $this->db->select('*');
        $this->db->from('remun_medis.jadwal_prosedur jp');
        $this->db->where(array('jp.ID_KAMAR' => $this->input->post('kamar'),'jp.TANGGAL' => $this->input->post('tanggal')));
        $query = $this->db->get();

        return $query->row();
    }

    function cekAntrian(){
        $loket = array("105060101"=>"A", "105020201"=>"B", "105020705"=>"C", "105020704"=>"D", "105120101"=>"E");

        $this->db->select('*');
        $this->db->from('kemkes.reservasi_antrian kr');
        $this->db->where(array('kr.PASIEN' => $this->input->post('norm'), 'kr.RUANGAN' => $loket[$this->input->post('idr')], 'kr.TANGGAL_KUNJUNGAN' => $this->input->post('tgl')));
        $this->db->where('(kr.KONTAK = "'.$this->input->post('nomor').'" OR kr.KONTAK IS NULL)');
        $query = $this->db->get();
        // $row = $query->row();
        $num = $query->num_rows();
        return $num;

    }

    function getNomor(){
        $this->db->select('rp.ID, rp.NOMR,rp.NAMAPASIEN,DATE_FORMAT(rp.TANGGAL,"%d/%m/%Y") TANGGAL, rp.NOMOR, mr.DESKRIPSI RUANGAN, `master`.getNamaLengkapPegawai(md.NIP) DOKTER');
        $this->db->from('remun_medis.perjanjian rp');
        $this->db->join('`master`.ruangan mr','rp.ID_RUANGAN = mr.ID','left');
        $this->db->join('`master`.dokter md','rp.ID_DOKTER = md.ID','left');
        $this->db->where(array('rp.ID_DOKTER' => $this->input->post('dokter'), 'rp.ID_RUANGAN' => $this->input->post('ruangan'), 'rp.TANGGAL' => $this->input->post('tanggal'), 'rp.STATUS !=' => 0));
        $this->db->order_by('NAMAPASIEN');
        $query = $this->db->get();
        return $query->result();
    }

    function kirimUlang(){
        $this->db->select('dip.ID, `master`.getNamaLengkapPegawai(md.NIP) DOKTER, r.DESKRIPSI RUANGAN
        ,DATE_FORMAT(j.TANGGAL,"%d/%m/%Y") TANGGAL ,p.NOMOR ,p.NOMR, p.NAMAPASIEN, ip.PESAN, dip.`STATUS`');
        $this->db->from('remun_medis.log_info_pasien ip');
        $this->db->join(' remun_medis.log_detil_info_pasien dip', 'ip.ID = dip.ID_INFO','left');
        $this->db->join('remun_medis.jadwal j', 'ip.ID_JADWAL = j.ID','left');
        $this->db->join('`master`.dokter md', 'j.DOKTER = md.ID','left');
        $this->db->join('`master`.ruangan r', 'j.RUANGAN = r.ID','left');
        $this->db->join('remun_medis.perjanjian p', 'dip.ID_PERJANJIAN = p.ID','left');
        $this->db->where(array('ip.ID' => $this->input->post('id'), 'dip.`STATUS` !=' => 'sent'));
        $query = $this->db->get();
        return $query->result();
    }

    function getId(){
        $this->db->select('rp.ID');
        $this->db->from('remun_medis.perjanjian rp');
        $this->db->where(array('rp.NOMR' => $this->input->post('norm'),'rp.ID_DOKTER' => $this->input->post('dokter'), 'rp.ID_RUANGAN' => $this->input->post('idr'), 'rp.TANGGAL' => $this->input->post('tgl')));
        $query = $this->db->get()->row();
        $id = $query -> ID;

        return $id;
    }

    function NoKontrol(){
        $query = $this->db->query('SELECT generator.generateNoKontrol("'.$this->input->post("tgl").'") KODE')->row();
        return $query -> KODE;
    }

    function NoKontrolDokter(){
        $getPenjamin = $this->getPenjamin();
        $query = $this->db->query('SELECT generator.generateNoKontrolDokter("'.$this->input->post("tgl").'","'.$this->input->post("dokter").'","'.$this->input->post("idr").'","'.$getPenjamin.'") KODE')->row();
        return $query -> KODE;
    }

    function getPenjamin(){
        $this->db->select('penj.ID, penj.DESKRIPSI, penpen.NORM');
        $this->db->from('pendaftaran.pendaftaran penpen');
        $this->db->join('pendaftaran.penjamin penpenj','penpen.NOMOR = penpenj.NOPEN','left');
        $this->db->join('master.referensi penj','penpenj.JENIS = penj.ID AND penj.JENIS = 10','left');
        $this->db->where('penpen.NORM', $this->input->post("norm"));
        $this->db->order_by('penpen.TANGGAL','DESC');
        $this->db->limit('1');
        $query = $this->db->get()->row();
        if ($query == null) {
            return 1;
        }else{
            return $query->ID;
        }
    }

    function cekPenjamin(){
        $this->db->select('COUNT(*) TOTAL
        , (SELECT IF(pj.JENIS=2,2,1) FROM pendaftaran.pendaftaran p
                LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR
            WHERE p.NORM=rp.NOMR AND p.`STATUS`!=0
            ORDER BY p.TANGGAL DESC
            LIMIT 1
            ) PENJAMIN');
        $this->db->from('`remun_medis`.`perjanjian` `rp`');
        $this->db->where('`rp`.`ID_DOKTER`', $this->input->post("dokter"));
        $this->db->where('`rp`.`TANGGAL`', $this->input->post("tgl"));
        $this->db->where('`rp`.`ID_RUANGAN`', $this->input->post("idr"));
        $this->db->where('`rp`.`STATUS` !=', 0);
        $this->db->where('`rp`.`RENCANA`', 1);
        $this->db->where('(SELECT IF(pj.JENIS=2, 2, 1) FROM pendaftaran.pendaftaran p 
        LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = p.NOMOR 
        WHERE p.NORM=rp.NOMR AND p.`STATUS`!=0 
        ORDER BY p.TANGGAL DESC LIMIT 1) = 1');
        $query = $this->db->get();
        return $query->row();
    }

    function cekRencana($flag=0){
        $this->db->select('COUNT(*) TOTAL');
        $this->db->from('remun_medis.perjanjian rp');
        $this->db->where('rp.STATUS !=', 0);
        $this->db->where('rp.RENCANA', $this->input->post('rencana'));
        if($flag == 3){
            $tanggal=$this->input->post('tgl');
            $this->db->where("rp.TANGGAL > DATE(NOW())");
            $this->db->where('rp.ID_RUANGAN', $this->input->post('idr'));
            $this->db->where('rp.ID_DOKTER', $this->input->post('dokter'));
            $this->db->where('rp.NOMR', $this->input->post('norm'));

        }elseif($flag == 2){
            $tanggal=$this->input->post('tgl');
            $this->db->where("rp.TANGGAL > DATE(NOW())");
            $this->db->where("rp.TANGGAL BETWEEN '$tanggal' - INTERVAL 3 DAY AND '$tanggal'");
            $this->db->where('rp.ID_RUANGAN', $this->input->post('idr'));
            $this->db->where('rp.ID_DOKTER', $this->input->post('dokter'));
            $this->db->where('rp.NOMR', $this->input->post('norm'));

        }else{
            $this->db->where('rp.TANGGAL', $this->input->post('tgl'));
        }
        
        if($flag == 1){
            $this->db->where('rp.ID_RUANGAN', $this->input->post('idr'));
            $this->db->where('rp.ID_DOKTER', $this->input->post('dokter'));
        }
        $query = $this->db->get()->row();
        return $query -> TOTAL;
    }

    function getHistory(){
        $this->db->select('p.ID ,p.NOMR, p.NAMAPASIEN, p.TANGGAL
        , `master`.getNamaLengkapPegawai(d.NIP) DOKTER
        , r.DESKRIPSI RUANGAN, c.DESKRIPSI RENCANA, p.NOKONTROL
        , p.INPUT CREATED_AT
        , po.NAMA CREATED_BY 
        , p.DELETED_AT
        , pd.NAMA DELETED_BY 
        , p.`STATUS`
        , p.DPJP');
        $this->db->from('remun_medis.perjanjian p');
        $this->db->join('`master`.dokter d','p.ID_DOKTER = d.ID','LEFT');
        $this->db->join('`master`.ruangan r','p.ID_RUANGAN=r.ID','LEFT');
        $this->db->join('remun_medis.rencana c','p.RENCANA = c.ID','LEFT');
        $this->db->join('aplikasi.pengguna po','p.OLEH = po.ID','LEFT');
        $this->db->join('aplikasi.pengguna pd','p.DELETED_BY = pd.ID','LEFT');
        $this->db->where('p.TANGGAL >= CURDATE()');

        $this->db->order_by(' p.TANGGAL','DESC');
        if($this->input->post('norm') != '')
        {
            $this->db->where('p.NOMR', $this->input->post('norm'));
        }

        $query = $this->db->get();
  		
  		return $query->result();
    }

    function updateDpjp()
    {
        $id = $this->input->post('id');
        $dpjp = $this->input->post('dpjp');
        $norm = $this->input->post('norm');
        $tanggal = $this->input->post('tgl');
        $oleh = $this->session->userdata('id');
        if ($this->input->post('oleh')) {
            $oleh = $this->input->post('oleh');
        }
        if (!$id || !$dpjp || !$norm || !$tanggal) {
            return array('status' => 400, 'message' => 'ID dan DPJP tidak boleh kosong.');
        }

        $this->db->select('ID');
        $this->db->from('remun_medis.perjanjian');
        $this->db->where('NOMR', $norm);
        $this->db->where('TANGGAL', $tanggal);
        $this->db->where('DPJP', 1);
        $row = $this->db->get()->row();

        if ($row && $row->ID != $id) {
            $this->db->where('ID', $row->ID);
            $this->db->update('remun_medis.perjanjian', array('DPJP' => 0));
        }

        $data = array(
            'DPJP' => $dpjp
        );
        $this->db->where('ID', $id);
        $this->db->update('remun_medis.perjanjian', $data);

        if ($this->db->affected_rows() > 0) {
            return array('status' => 200, 'message' => 'DPJP berhasil diubah.');
        } else {
            return array('status' => 400, 'message' => 'Tidak ada perubahan data.');
        }
    }
}
