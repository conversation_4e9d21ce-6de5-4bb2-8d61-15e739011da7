<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Dashboard extends Backend_Controller{

	function __construct(){
        parent::__construct();

        if($this->session->userdata('logged_in') == false ){
            redirect('login');
        }
    }

	public function index(){
		if(!in_array(1,$this->session->userdata('akses'))){
			$this->site->view('error_403');
		}else{			
			$this->site->view('dashboard');
		}
	}

	public function signout(){
		unset($_SESSION);
        session_destroy();
        redirect('login');
	}

	public function setModule(){
		$this->session->set_userdata(array('module'=> $this->uri->segment(3)));

		if($this->uri->segment(3) == 1){
			redirect('dashboard');
		}elseif($this->uri->segment(3) == 2){
			redirect('radiologi/dashboard');
		}else{
			redirect('prosedur/dashboard');
		}
	}

}