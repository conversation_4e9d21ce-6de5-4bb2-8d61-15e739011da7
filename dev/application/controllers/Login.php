<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Login extends Backend_Controller{

	function __construct() {
        parent::__construct();
        $this->load->model(array('Login_model'));

        if($this->session->userdata('logged_in') == true ){
            redirect($this->session->userdata('link'));
        }

    }

	public 	function index(){
        echo $this->session->userdata('link');
		$this->site->view('index');
	}

	public function signin(){
        $result = $this->Login_model->login();
        if(isset($result['data'])){
            $data = $result['data'];
            $session = array(
                'id'            => $data['id'],
                'username'      => $data['username'],
                'nama'          => $data['nama'],
                'akses'         => explode(",",$data['akses']),
                'fitur'         => explode(",",$data['fitur']),
                'module'        => $data['module'],
                'module_akses'  => explode(",",$data['module_akses']),
                'link'          => $data['link'],
                'logged_in'     => TRUE
            );

            $this->session->set_userdata($session);
            // redirect('dashboard');
        }
        echo json_encode($result);
    }

}