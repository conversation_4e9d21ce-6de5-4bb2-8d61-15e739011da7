<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Controller untuk pengiriman WhatsApp otomatis via Cron
 * 
 * URL Format:
 * /cron_whatsapp/kirim_reminder_h1?dokter=ID&ruangan=ID&tanggal=YYYY-MM-DD&rencana=ID&token=SECRET
 * /cron_whatsapp/kirim_reminder_h1_all?tanggal=YYYY-MM-DD&token=SECRET
 * /cron_whatsapp/view_pasien_h1?dokter=ID&ruangan=ID&tanggal=YYYY-MM-DD&rencana=ID&format=html&token=SECRET
 * /cron_whatsapp/dashboard?tanggal=YYYY-MM-DD&format=html&token=SECRET
 * /cron_whatsapp/status?tanggal=YYYY-MM-DD&token=SECRET
 */
class Cron_whatsapp extends Backend_Controller {

    private $cron_token = 'perjanjian_cron_2024'; // Token keamanan untuk akses cron

    function __construct() {
        parent::__construct();
        $this->load->model(array('Perjanjian_model', 'Cron_whatsapp_model'));
        $this->load->library('whatsapp');
        
        // Set timezone
        date_default_timezone_set('Asia/Jakarta');
    }

    /**
     * Validasi token keamanan untuk akses cron
     */
    private function validate_token() {
        $token = $this->input->get('token');
        if ($token !== $this->cron_token) {
            $this->output
                ->set_status_header(401)
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'status' => 'error',
                    'message' => 'Token tidak valid'
                ]));
            return false;
        }
        return true;
    }

    /**
     * Kirim reminder H-1 untuk dokter/ruangan/tanggal tertentu
     * 
     * Parameter:
     * - dokter: ID dokter (opsional)
     * - ruangan: ID ruangan (opsional) 
     * - tanggal: Tanggal perjanjian (YYYY-MM-DD, default: besok)
     * - rencana: ID rencana (opsional)
     * - pesan: Pesan custom (opsional)
     * - token: Token keamanan (wajib)
     */
    public function kirim_reminder_h1() {
        if (!$this->validate_token()) {
            return;
        }

        $dokter = $this->input->get('dokter');
        $ruangan = $this->input->get('ruangan');
        $tanggal = $this->input->get('tanggal') ?: date('Y-m-d', strtotime('+1 day'));
        $rencana = $this->input->get('rencana');
        $pesan = $this->input->get('pesan') ?: 'default';

        try {
            // Ambil data perjanjian H-1
            $perjanjian_list = $this->Cron_whatsapp_model->get_perjanjian_h1([
                'dokter' => $dokter,
                'ruangan' => $ruangan,
                'tanggal' => $tanggal,
                'rencana' => $rencana
            ]);

            if (empty($perjanjian_list)) {
                $this->output
                    ->set_content_type('application/json')
                    ->set_output(json_encode([
                        'status' => 'info',
                        'message' => 'Tidak ada perjanjian ditemukan untuk parameter yang diberikan',
                        'data' => [
                            'dokter' => $dokter,
                            'ruangan' => $ruangan,
                            'tanggal' => $tanggal,
                            'rencana' => $rencana
                        ]
                    ]));
                return;
            }

            // Log aktivitas cron
            $log_cron_id = $this->Cron_whatsapp_model->log_cron_activity([
                'type' => 'reminder_h1',
                'parameters' => json_encode([
                    'dokter' => $dokter,
                    'ruangan' => $ruangan,
                    'tanggal' => $tanggal,
                    'rencana' => $rencana
                ]),
                'total_perjanjian' => count($perjanjian_list),
                'pesan' => $pesan
            ]);

            $berhasil = 0;
            $gagal = 0;
            $detail_hasil = [];

            foreach ($perjanjian_list as $perjanjian) {
                // Jika nomor diawali 0, ubah jadi +62, jika sudah +62 biarkan, jika diawali 62 tambahkan +
                $nomor_raw = trim($perjanjian->NOMOR);
                if (strpos($nomor_raw, '0') === 0) {
                    $nomor = '+62' . substr($nomor_raw, 1);
                } elseif (strpos($nomor_raw, '+62') === 0) {
                    $nomor = $nomor_raw;
                } elseif (strpos($nomor_raw, '62') === 0) {
                    $nomor = '+' . $nomor_raw;
                } else {
                    $nomor = $nomor_raw;
                }
                // $nomor = '+6281298652366';

                if($pesan === 'default') {
                    $pesan_custom = 'Reminder: Anda memiliki perjanjian besok. Mohon datang tepat waktu.';
                } else if($pesan === 'sep30menit') {
                    $estimasi = date('H:i', strtotime($perjanjian->AWAL)) . ' - ' . date('H:i', strtotime($perjanjian->AKHIR));
                    $mulai = date('H:i', strtotime($perjanjian->AWAL) - 1800); // 1800 detik = 30 menit
                    $pesan_custom = "Dalam rangka peningkatan pelayanan pasien, Pendaftaran melalui KIOSK SEP hanya bisa dilakukan minimal 30 menit sebelum estimasi dilayani. Estimasi dilayani anda pada pukul *$estimasi*. Mohon datang dan lakukan pendaftaran dimulai pukul *$mulai*. Untuk informasi lebih lanjut dapat menghubungi Customer Care dengan nomor 0878-7788-8890.";
                }

                // Format pesan WhatsApp
                $pesan_wa = [
                    'dengan RM *' . $perjanjian->NOMR . '*. Perjanjian dengan dokter *' . $perjanjian->DOKTER . '* ruangan *' . $perjanjian->RUANGAN .'*',
                    $perjanjian->TANGGAL_FORMAT,
                    $pesan_custom
                ];

                try {
                    $response = $this->whatsapp->send($nomor, $pesan_wa);
                    $status = isset($response['data']['data'][0]['status']) ? $response['data']['data'][0]['status'] : 'unknown';
                    
                    if ($status === 'sent' || $status === 'delivered') {
                        $berhasil++;
                    } else {
                        $gagal++;
                    }

                    // Log detail pengiriman
                    $this->Cron_whatsapp_model->log_detail_pengiriman([
                        'id_log_cron' => $log_cron_id,
                        'id_perjanjian' => $perjanjian->ID,
                        'nomor' => $nomor,
                        'pesan' => json_encode($pesan_wa),
                        'response' => json_encode($response),
                        'status' => $status
                    ]);

                    $detail_hasil[] = [
                        'nomr' => $perjanjian->NOMR,
                        'nama' => $perjanjian->NAMAPASIEN,
                        'nomor' => $nomor,
                        'status' => $status
                    ];

                } catch (Exception $e) {
                    $gagal++;
                    
                    // Log error
                    $this->Cron_whatsapp_model->log_detail_pengiriman([
                        'id_log_cron' => $log_cron_id,
                        'id_perjanjian' => $perjanjian->ID,
                        'nomor' => $nomor,
                        'pesan' => json_encode($pesan_wa),
                        'response' => json_encode(['error' => $e->getMessage()]),
                        'status' => 'error'
                    ]);

                    $detail_hasil[] = [
                        'nomr' => $perjanjian->NOMR,
                        'nama' => $perjanjian->NAMAPASIEN,
                        'nomor' => $nomor,
                        'status' => 'error',
                        'error' => $e->getMessage()
                    ];
                }

                // Delay untuk menghindari rate limiting
                usleep(500000); // 0.5 detik
            }

            // Update log dengan hasil akhir
            $this->Cron_whatsapp_model->update_log_cron($log_cron_id, [
                'berhasil' => $berhasil,
                'gagal' => $gagal,
                'status' => 'completed'
            ]);

            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'status' => 'success',
                    'message' => 'Pengiriman reminder H-1 selesai',
                    'data' => [
                        'total_perjanjian' => count($perjanjian_list),
                        'berhasil' => $berhasil,
                        'gagal' => $gagal,
                        'log_id' => $log_cron_id,
                        'detail' => $detail_hasil
                    ]
                ]));

        } catch (Exception $e) {
            $this->output
                ->set_status_header(500)
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'status' => 'error',
                    'message' => 'Terjadi kesalahan: ' . $e->getMessage()
                ]));
        }
    }

    /**
     * Kirim reminder H-1 untuk semua perjanjian pada tanggal tertentu
     * 
     * Parameter:
     * - tanggal: Tanggal perjanjian (YYYY-MM-DD, default: besok)
     * - pesan: Pesan custom (opsional)
     * - token: Token keamanan (wajib)
     */
    public function kirim_reminder_h1_all() {
        if (!$this->validate_token()) {
            return;
        }

        $tanggal = $this->input->get('tanggal') ?: date('Y-m-d', strtotime('+1 day'));
        $pesan_custom = $this->input->get('pesan') ?: 'Reminder: Anda memiliki perjanjian besok. Mohon datang tepat waktu.';

        // Panggil method kirim_reminder_h1 tanpa filter dokter/ruangan
        $_GET['dokter'] = null;
        $_GET['ruangan'] = null;
        $_GET['tanggal'] = $tanggal;
        $_GET['pesan'] = $pesan_custom;
        
        $this->kirim_reminder_h1();
    }

    /**
     * Cek status dan log pengiriman
     */
    public function status() {
        if (!$this->validate_token()) {
            return;
        }

        $log_id = $this->input->get('log_id');
        $tanggal = $this->input->get('tanggal') ?: date('Y-m-d');

        if ($log_id) {
            $log = $this->Cron_whatsapp_model->get_log_detail($log_id);
        } else {
            $log = $this->Cron_whatsapp_model->get_log_by_date($tanggal);
        }

        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode([
                'status' => 'success',
                'data' => $log
            ]));
    }

    /**
     * View daftar pasien yang akan mendapat reminder H-1
     *
     * Parameter:
     * - dokter: ID dokter (opsional)
     * - ruangan: ID ruangan (opsional)
     * - tanggal: Tanggal perjanjian (YYYY-MM-DD, default: besok)
     * - rencana: ID rencana (opsional)
     * - format: Format output (json/html, default: json)
     * - token: Token keamanan (wajib)
     */
    public function view_pasien_h1() {
        if (!$this->validate_token()) {
            return;
        }

        $dokter = $this->input->get('dokter');
        $ruangan = $this->input->get('ruangan');
        $tanggal = $this->input->get('tanggal') ?: date('Y-m-d', strtotime('+1 day'));
        $rencana = $this->input->get('rencana');
        $format = $this->input->get('format') ?: 'json';

        try {
            // Ambil data perjanjian H-1
            $perjanjian_list = $this->Cron_whatsapp_model->get_perjanjian_h1([
                'dokter' => $dokter,
                'ruangan' => $ruangan,
                'tanggal' => $tanggal,
                'rencana' => $rencana
            ]);

            // Hitung statistik
            $total_pasien = count($perjanjian_list);
            $pasien_dengan_nomor = 0;
            $pasien_tanpa_nomor = 0;
            $dokter_list = [];
            $ruangan_list = [];

            foreach ($perjanjian_list as $perjanjian) {
                if (!empty($perjanjian->NOMOR)) {
                    $pasien_dengan_nomor++;
                } else {
                    $pasien_tanpa_nomor++;
                }

                // Kumpulkan unique dokter dan ruangan
                if (!in_array($perjanjian->DOKTER, $dokter_list)) {
                    $dokter_list[] = $perjanjian->DOKTER;
                }
                if (!in_array($perjanjian->RUANGAN, $ruangan_list)) {
                    $ruangan_list[] = $perjanjian->RUANGAN;
                }
            }

            $response_data = [
                'status' => 'success',
                'message' => 'Data pasien H-1 berhasil diambil',
                'filter' => [
                    'dokter' => $dokter,
                    'ruangan' => $ruangan,
                    'tanggal' => $tanggal,
                    'rencana' => $rencana
                ],
                'statistik' => [
                    'total_pasien' => $total_pasien,
                    'pasien_dengan_nomor' => $pasien_dengan_nomor,
                    'pasien_tanpa_nomor' => $pasien_tanpa_nomor,
                    'total_dokter' => count($dokter_list),
                    'total_ruangan' => count($ruangan_list)
                ],
                'data' => $perjanjian_list
            ];

            if ($format === 'html') {
                $this->site->view('cron_whatsapp/view_pasien_h1', $response_data);
            } else {
                $this->output
                    ->set_content_type('application/json')
                    ->set_output(json_encode($response_data, JSON_PRETTY_PRINT));
            }

        } catch (Exception $e) {
            $this->output
                ->set_status_header(500)
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'status' => 'error',
                    'message' => 'Terjadi kesalahan: ' . $e->getMessage()
                ]));
        }
    }

    /**
     * Dashboard ringkasan perjanjian H-1
     *
     * Parameter:
     * - tanggal: Tanggal perjanjian (YYYY-MM-DD, default: besok)
     * - format: Format output (json/html, default: html)
     * - token: Token keamanan (wajib)
     */
    public function dashboard() {
        if (!$this->validate_token()) {
            return;
        }

        $tanggal = $this->input->get('tanggal') ?: date('Y-m-d', strtotime('+1 day'));
        $format = $this->input->get('format') ?: 'html';

        try {
            // Ambil statistik umum
            $statistik_umum = $this->Cron_whatsapp_model->get_statistik_perjanjian_h1(['tanggal' => $tanggal]);

            // Ambil ringkasan per dokter
            $ringkasan_dokter = $this->Cron_whatsapp_model->get_ringkasan_per_dokter($tanggal);

            // Ambil ringkasan per ruangan
            $ringkasan_ruangan = $this->Cron_whatsapp_model->get_ringkasan_per_ruangan($tanggal);

            $response_data = [
                'status' => 'success',
                'tanggal' => $tanggal,
                'tanggal_format' => date('d/m/Y', strtotime($tanggal)),
                'statistik_umum' => $statistik_umum,
                'ringkasan_dokter' => $ringkasan_dokter,
                'ringkasan_ruangan' => $ringkasan_ruangan
            ];

            if ($format === 'html') {
                $this->site->view('cron_whatsapp/dashboard', $response_data);
            } else {
                $this->output
                    ->set_content_type('application/json')
                    ->set_output(json_encode($response_data, JSON_PRETTY_PRINT));
            }

        } catch (Exception $e) {
            $this->output
                ->set_status_header(500)
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'status' => 'error',
                    'message' => 'Terjadi kesalahan: ' . $e->getMessage()
                ]));
        }
    }

    /**
     * Health check untuk monitoring cron
     */
    public function health() {
        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode([
                'status' => 'ok',
                'timestamp' => date('Y-m-d H:i:s'),
                'server_time' => time(),
                'timezone' => date_default_timezone_get()
            ]));
    }
}
