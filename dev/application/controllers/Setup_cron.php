<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Controller untuk setup dan inisialisasi sistem cron WhatsApp
 */
class Setup_cron extends CI_Controller {

    private $setup_token = 'setup_cron_perjanjian_2024'; // Token untuk setup

    function __construct() {
        parent::__construct();
        $this->load->model('Cron_whatsapp_model');
    }

    /**
     * Validasi token setup
     */
    private function validate_setup_token() {
        $token = $this->input->get('token');
        if ($token !== $this->setup_token) {
            $this->output
                ->set_status_header(401)
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'status' => 'error',
                    'message' => 'Token setup tidak valid'
                ]));
            return false;
        }
        return true;
    }

    /**
     * Buat tabel-tabel yang diperlukan untuk logging cron
     * 
     * URL: /setup_cron/create_tables?token=setup_cron_perjanjian_2024
     */
    public function create_tables() {
        if (!$this->validate_setup_token()) {
            return;
        }

        try {
            $this->Cron_whatsapp_model->create_log_tables();
            
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'status' => 'success',
                    'message' => 'Tabel logging cron berhasil dibuat',
                    'tables' => [
                        'remun_medis.log_cron_whatsapp',
                        'remun_medis.log_detail_cron_whatsapp'
                    ]
                ]));
                
        } catch (Exception $e) {
            $this->output
                ->set_status_header(500)
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'status' => 'error',
                    'message' => 'Gagal membuat tabel: ' . $e->getMessage()
                ]));
        }
    }

    /**
     * Test koneksi database dan WhatsApp API
     * 
     * URL: /setup_cron/test_connection?token=setup_cron_perjanjian_2024
     */
    public function test_connection() {
        if (!$this->validate_setup_token()) {
            return;
        }

        $results = [];

        // Test database connection
        try {
            $this->db->query('SELECT 1');
            $results['database'] = [
                'status' => 'success',
                'message' => 'Koneksi database berhasil'
            ];
        } catch (Exception $e) {
            $results['database'] = [
                'status' => 'error',
                'message' => 'Koneksi database gagal: ' . $e->getMessage()
            ];
        }

        // Test WhatsApp library
        try {
            $this->load->library('whatsapp');
            $results['whatsapp'] = [
                'status' => 'success',
                'message' => 'Library WhatsApp berhasil dimuat'
            ];
        } catch (Exception $e) {
            $results['whatsapp'] = [
                'status' => 'error',
                'message' => 'Library WhatsApp gagal dimuat: ' . $e->getMessage()
            ];
        }

        // Test model
        try {
            $dokter_list = $this->Cron_whatsapp_model->get_dokter_aktif();
            $results['model'] = [
                'status' => 'success',
                'message' => 'Model berhasil diakses',
                'data' => [
                    'total_dokter' => count($dokter_list)
                ]
            ];
        } catch (Exception $e) {
            $results['model'] = [
                'status' => 'error',
                'message' => 'Model gagal diakses: ' . $e->getMessage()
            ];
        }

        $overall_status = 'success';
        foreach ($results as $test) {
            if ($test['status'] === 'error') {
                $overall_status = 'error';
                break;
            }
        }

        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode([
                'status' => $overall_status,
                'message' => 'Test koneksi selesai',
                'results' => $results
            ]));
    }

    /**
     * Ambil informasi referensi untuk parameter cron
     * 
     * URL: /setup_cron/get_references?token=setup_cron_perjanjian_2024
     */
    public function get_references() {
        if (!$this->validate_setup_token()) {
            return;
        }

        try {
            $data = [
                'dokter' => $this->Cron_whatsapp_model->get_dokter_aktif(),
                'ruangan' => $this->Cron_whatsapp_model->get_ruangan_aktif(),
                'rencana' => $this->Cron_whatsapp_model->get_rencana()
            ];

            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'status' => 'success',
                    'message' => 'Data referensi berhasil diambil',
                    'data' => $data
                ]));

        } catch (Exception $e) {
            $this->output
                ->set_status_header(500)
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'status' => 'error',
                    'message' => 'Gagal mengambil data referensi: ' . $e->getMessage()
                ]));
        }
    }

    /**
     * Test pengiriman WhatsApp ke nomor test
     * 
     * URL: /setup_cron/test_whatsapp?nomor=081234567890&token=setup_cron_perjanjian_2024
     */
    public function test_whatsapp() {
        if (!$this->validate_setup_token()) {
            return;
        }

        $nomor = $this->input->get('nomor');
        if (!$nomor) {
            $this->output
                ->set_status_header(400)
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'status' => 'error',
                    'message' => 'Parameter nomor diperlukan'
                ]));
            return;
        }

        try {
            $this->load->library('whatsapp');
            
            $nomor_format = '+62' . substr(trim($nomor), 1);
            $pesan = [
                'Test pengiriman WhatsApp dari sistem cron perjanjian',
                date('d/m/Y H:i:s'),
                'Jika Anda menerima pesan ini, sistem WhatsApp berfungsi dengan baik.'
            ];

            $response = $this->whatsapp->send($nomor_format, $pesan);
            
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'status' => 'success',
                    'message' => 'Test WhatsApp berhasil dikirim',
                    'data' => [
                        'nomor' => $nomor_format,
                        'pesan' => $pesan,
                        'response' => $response
                    ]
                ]));

        } catch (Exception $e) {
            $this->output
                ->set_status_header(500)
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'status' => 'error',
                    'message' => 'Test WhatsApp gagal: ' . $e->getMessage()
                ]));
        }
    }

    /**
     * Tampilkan informasi sistem dan dokumentasi
     */
    public function info() {
        $info = [
            'system' => [
                'name' => 'Sistem Cron WhatsApp Perjanjian',
                'version' => '1.0.0',
                'description' => 'Sistem otomatis untuk mengirim reminder WhatsApp H-1 perjanjian',
                'timezone' => date_default_timezone_get(),
                'server_time' => date('Y-m-d H:i:s')
            ],
            'endpoints' => [
                'reminder_h1' => [
                    'url' => base_url('cron_whatsapp/kirim_reminder_h1'),
                    'method' => 'GET',
                    'parameters' => [
                        'dokter' => 'ID dokter (opsional)',
                        'ruangan' => 'ID ruangan (opsional)',
                        'tanggal' => 'Tanggal perjanjian YYYY-MM-DD (default: besok)',
                        'rencana' => 'ID rencana (opsional)',
                        'pesan' => 'Pesan custom (opsional)',
                        'token' => 'Token keamanan (wajib)'
                    ]
                ],
                'reminder_h1_all' => [
                    'url' => base_url('cron_whatsapp/kirim_reminder_h1_all'),
                    'method' => 'GET',
                    'parameters' => [
                        'tanggal' => 'Tanggal perjanjian YYYY-MM-DD (default: besok)',
                        'pesan' => 'Pesan custom (opsional)',
                        'token' => 'Token keamanan (wajib)'
                    ]
                ],
                'status' => [
                    'url' => base_url('cron_whatsapp/status'),
                    'method' => 'GET',
                    'parameters' => [
                        'log_id' => 'ID log tertentu (opsional)',
                        'tanggal' => 'Tanggal log YYYY-MM-DD (default: hari ini)',
                        'token' => 'Token keamanan (wajib)'
                    ]
                ]
            ],
            'cron_examples' => [
                'daily_reminder' => [
                    'description' => 'Kirim reminder setiap hari jam 18:00 untuk perjanjian besok',
                    'cron' => '0 18 * * * curl "' . base_url('cron_whatsapp/kirim_reminder_h1_all?token=perjanjian_cron_2024') . '"'
                ],
                'specific_doctor' => [
                    'description' => 'Kirim reminder untuk dokter tertentu',
                    'cron' => '0 18 * * * curl "' . base_url('cron_whatsapp/kirim_reminder_h1?dokter=123&token=perjanjian_cron_2024') . '"'
                ]
            ]
        ];

        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode($info, JSON_PRETTY_PRINT));
    }
}
