<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON><PERSON><PERSON><PERSON> extends Backend_Controller{

	function __construct() {
        parent::__construct();
        $this->load->model(array('Perjanjian_model'));
        $this->load->library('whatsapp');

        if($this->session->userdata('logged_in') == false ){
            redirect('login');
        }
    }

	public 	function index(){

        $data = array(
                'nomr'       =>  $this->input->post('nomr'),
                'nama'       =>  $this->input->post('nama'),    
                'nomor'       =>  $this->input->post('nomor'),    

                // 'birth'      =>  $this->input->post('tgl_lahir')
                );

                if(!in_array(1,$this->session->userdata('akses'))){
                    $this->site->view('error_403');
                }else{
                    $this->site->view('perjanjian/index',$data);
                }
	}

    public function datatables(){
        $result = $this->Perjanjian_model->datatables();
        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            $sub_array[] = $row -> ID;
            // $sub_array[] = $row -> NOMR;
            $sub_array[] = $row -> NAMAPASIEN."<b> [ ".$row -> NOMR." ]</b>";
            $sub_array[] = $row -> TANGGAL;
            $sub_array[] = $row -> NOKONTROL;
            $sub_array[] = $row -> DOKTER;
            $sub_array[] = $row -> RUANGAN;
            $sub_array[] = $row -> RENCANA;
            $sub_array[] = $row -> STATUS_SORE == 0 ? "Poli Pagi" : "Poli Sore";
            // $sub_array[] = $row -> STATUS == 1 ? '<i class="far fa-check-square fa-2x hapus" id="'.$row -> ID.'"></i>' : '<i class="far fa-square fa-2x hapus" id="'.$row -> ID.'"></i>';

            $data[] = $sub_array;
        }

                $output = array(
            "draw"              => intval($_POST["draw"]),
            "recordsTotal"      => $this->Perjanjian_model->total_count(),
            "recordsFiltered"   => $this->Perjanjian_model->filter_count(),
            "data"               => $data
        );
        echo json_encode($output);
    }

    public function create()
    {
        // $result = $this->Perjanjian_model->cek();
        // if($result['status'] != 503){
        //     $result = $this->Perjanjian_model->create();
        // }
        $result = $this->Perjanjian_model->cek();

        if($result['status'] == 200){
            if(isset($result['data'])){
                $status_sore = $result['data']['status_sore'];
                $_POST['statusSore'] = $status_sore;
            }
            $result = $this->Perjanjian_model->create();
        }

        echo json_encode($result);
    }

    public function batalPasien()
    {
        // $result = $this->Perjanjian_model->cek();
        // if($result['status'] != 503){
        //     $result = $this->Perjanjian_model->create();
        // }
        $result = $this->Perjanjian_model->batalPasien();
        echo json_encode($result);
    }

    public function batalDokter()
    {
        $this->db->trans_begin();
        $resultBatal = $this->Perjanjian_model->batalDokter();

        if($resultBatal['status'] == 200){
            $resultNomor = $this->Perjanjian_model->getNomor();

            foreach ($resultNomor as $row){
                $nomor = '+62'.substr(trim($row -> NOMOR), 1);
                // $nomor = '+6281298652366';
                $pesan = ['dengan RM '. $row -> NOMR .'. Perjanjian dengan dokter '. $row -> DOKTER .' ruangan '. $row -> RUANGAN , $row -> TANGGAL , 'kami mohon maaf dokter tidak praktek silahkan daftar online kembali '];
    
                // echo $nomor .' -> '. $pesan . '<br>';
                $_POST['id'] = $row -> ID;
                $this->Perjanjian_model->batalPasien();

                try {
                    $this->whatsapp->send($nomor, $pesan);
                }
                catch(Exception $e) {
                    // echo 'Message: ' .$e->getMessage();
                }
            }
        }
        
        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }

        echo json_encode($result);
    }

    public function cek(){
        $result = $this->Perjanjian_model->cek();
        echo json_encode($result);
    }

    // public function get_id(){
    //     $result = $this->Perjanjian_model->get_id();
    //     print_r($result -> ID);
    // }

    public function getNomor(){
        $result = $this->Perjanjian_model->getNomor();
        echo json_encode($result);
    }

    // public function tes(){
    //     $nomor = '+62'.substr(trim('081298652366'), 1);
    //     $pesan = 'Yth Pasien RS Kanker Dharmais dengan RM 224723. Perjanjian dengan dokter Yanto ruangan Poli Onkologi 2 pada tanggal 06-01-2019 Dibatalkan';
    //     echo json_encode($this->whatsapp->send($nomor, $pesan));
    // }

    public function getHistory(){
        $post = $this->input->post(NULL,TRUE);
        $result = $this->Perjanjian_model->getHistory();
                    
        echo json_encode(array(
            'status' => 'success',
            'data' => $result
        ));
    }

    public function updateDpjp()
    {
        $id = $this->input->post('id');
        $dpjp = $this->input->post('dpjp');
        if (empty($id) || ($dpjp === null || $dpjp === '')) {
            echo json_encode(['status' => 'failed', 'message' => 'ID dan DPJP harus diisi']);
            return;
        }

        $update = $this->Perjanjian_model->updateDPJP($id, $dpjp);

        if ($update) {
            echo json_encode(['status' => 'success', 'message' => 'DPJP berhasil diupdate']);
        } else {
            echo json_encode(['status' => 'failed', 'message' => 'Gagal mengupdate DPJP']);
        }
    }

    public function cekJadwalKamar(){
        $data = $this->Perjanjian_model->cekJadwalKamar();
        $result = array('status' => "0", 'message' => "Kamar tidak tersedia");
        if(isset($data -> ID)){
            $_POST['idj'] = $data -> ID;
            $cekKuota = $this->Perjanjian_model->cekKuotaKamar();
        }

        if(isset($cekKuota -> STATUS)){
            $result = array('status' => "0", 'message' => "Kuota kamar sudah penuh");
            if($cekKuota -> STATUS == 1){
                $result = array('status' => "1", 'message' => "Kamar tersedia", 'data' => $data);
            }
        }

        echo json_encode($result);
        
    }

    public function kirimPesan()
    {
        $this->db->trans_begin();

        $resultNomor = $this->Perjanjian_model->getNomor();
        $text = $this->input->post('pesan');
        $jadwal = $this->db->get_where('remun_medis.jadwal', array('DOKTER' => $this->input->post('dokter'),'RUANGAN' => $this->input->post('ruangan'),'TANGGAL' => $this->input->post('tanggal'), 'STATUS !=' => 0))->row();
        $data_info_pasien = array(
            'ID_JADWAL' => $jadwal->ID,
            'PESAN' => $text,
            'TANGGAL' => date('Y-m-d H:i:s'),
        );
        $this->db->insert('remun_medis.log_info_pasien', $data_info_pasien);
        $id_info_pasien = $this->db->insert_id();


        foreach ($resultNomor as $row){
            $nomor = '+62'.substr(trim($row -> NOMOR), 1);
            $pesan = ['dengan RM '. $row -> NOMR .'. Perjanjian dengan dokter '. $row -> DOKTER .' ruangan '. $row -> RUANGAN , $row -> TANGGAL , $text];
            try {
                $res = $this->whatsapp->send($nomor, $pesan);
                $status = $res['data']['data'][0]['status'];
            }
            catch(Exception $e) {}
            $data_detil_info_pasien = array(
                'ID_INFO' => $id_info_pasien,
                'ID_PERJANJIAN' => $row -> ID,
                'TANGGAL' => date('Y-m-d H:i:s'),
                'RESPONSE' => json_encode($res),
                'STATUS' => $status,
            );
            $this->db->insert('remun_medis.log_detil_info_pasien', $data_detil_info_pasien);
        }
        
        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }

        echo json_encode($result);
    }

    public function kirimUlangPesan()
    {
        $this->db->trans_begin();

        $result = $this->Perjanjian_model->kirimUlang();

        foreach ($result as $row){
            $nomor = '+62'.substr(trim($row -> NOMOR), 1);
            // $nomor = '+6281298652366';
            $pesan = ['dengan RM '. $row -> NOMR .'. Perjanjian dengan dokter '. $row -> DOKTER .' ruangan '. $row -> RUANGAN , $row -> TANGGAL , $row -> PESAN];
            try {
                $res = $this->whatsapp->send($nomor, $pesan);
                $status = $res['data']['data'][0]['status'];
            }
            catch(Exception $e) {}
            $data_detil_info_pasien = array(
                'TANGGAL' => date('Y-m-d H:i:s'),
                'RESPONSE' => json_encode($res),
                'STATUS' => $status,
            );
            $this->db->where('ID',$row -> ID);
            $this->db->update('remun_medis.log_detil_info_pasien', $data_detil_info_pasien);
        }
        
        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 'success');
        }

        echo json_encode($result);
    }
}
