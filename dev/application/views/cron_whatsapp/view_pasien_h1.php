<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daftar Pasien Reminder H-1</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            border-bottom: 2px solid #007bff;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        .header h1 {
            color: #007bff;
            margin: 0;
        }
        .filter-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .filter-info h3 {
            margin-top: 0;
            color: #495057;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .stat-card {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-card.success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
        }
        .stat-card.warning {
            background: linear-gradient(135deg, #ffc107, #e0a800);
        }
        .stat-card.info {
            background: linear-gradient(135deg, #17a2b8, #138496);
        }
        .stat-card.danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .table-container {
            overflow-x: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #007bff;
            color: white;
            font-weight: bold;
        }
        tr:hover {
            background-color: #f8f9fa;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .status-ada {
            background-color: #d4edda;
            color: #155724;
        }
        .status-kosong {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status-terkirim {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        .status-belum {
            background-color: #fff3cd;
            color: #856404;
        }
        .status-gagal {
            background-color: #f8d7da;
            color: #721c24;
        }
        .no-data {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
        .actions {
            margin-top: 20px;
            text-align: center;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #545b62;
        }
        .refresh-info {
            text-align: center;
            margin-top: 20px;
            color: #6c757d;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📱 Daftar Pasien Reminder H-1</h1>
            <p>Sistem Cron WhatsApp - Perjanjian Rumah Sakit</p>
        </div>

        <div class="filter-info">
            <h3>🔍 Filter yang Diterapkan</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
                <div><strong>Dokter:</strong> <?= $filter['dokter'] ?: 'Semua' ?></div>
                <div><strong>Ruangan:</strong> <?= $filter['ruangan'] ?: 'Semua' ?></div>
                <div><strong>Tanggal:</strong> <?= date('d/m/Y', strtotime($filter['tanggal'])) ?></div>
                <div><strong>Rencana:</strong> <?= $filter['rencana'] ?: 'Semua' ?></div>
            </div>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number"><?= $statistik['total_pasien'] ?></div>
                <div class="stat-label">Total Pasien</div>
            </div>
            <div class="stat-card success">
                <div class="stat-number"><?= $statistik['pasien_dengan_nomor'] ?></div>
                <div class="stat-label">Dengan Nomor HP</div>
            </div>
            <div class="stat-card info">
                <div class="stat-number"><?= $statistik['sudah_terkirim'] ?></div>
                <div class="stat-label">Sudah Terkirim</div>
            </div>
            <div class="stat-card warning">
                <div class="stat-number"><?= $statistik['belum_terkirim'] ?></div>
                <div class="stat-label">Belum Terkirim</div>
            </div>
            <?php if ($statistik['gagal_kirim'] > 0): ?>
            <div class="stat-card danger">
                <div class="stat-number"><?= $statistik['gagal_kirim'] ?></div>
                <div class="stat-label">Gagal Kirim</div>
            </div>
            <?php endif; ?>
        </div>

        <?php if (empty($data)): ?>
            <div class="no-data">
                <h3>📭 Tidak Ada Data</h3>
                <p>Tidak ada perjanjian ditemukan untuk filter yang diterapkan.</p>
            </div>
        <?php else: ?>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>No</th>
                            <th>No. RM</th>
                            <th>Nama Pasien</th>
                            <th>Dokter</th>
                            <th>Ruangan</th>
                            <th>Tanggal</th>
                            <th>Rencana</th>
                            <th>Nomor HP</th>
                            <th>Status HP</th>
                            <th>Status Kirim</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($data as $index => $pasien): ?>
                            <tr>
                                <td><?= $index + 1 ?></td>
                                <td><strong><?= htmlspecialchars($pasien->NOMR) ?></strong></td>
                                <td><?= htmlspecialchars($pasien->NAMAPASIEN) ?></td>
                                <td><?= htmlspecialchars($pasien->DOKTER) ?></td>
                                <td><?= htmlspecialchars($pasien->RUANGAN) ?></td>
                                <td><?= htmlspecialchars($pasien->TANGGAL_FORMAT) ?></td>
                                <td><?= htmlspecialchars($pasien->RENCANA ?: '-') ?></td>
                                <td><?= htmlspecialchars($pasien->NOMOR ?: '-') ?></td>
                                <td>
                                    <?php if (!empty($pasien->NOMOR)): ?>
                                        <span class="status-badge status-ada">✓ Ada Nomor</span>
                                    <?php else: ?>
                                        <span class="status-badge status-kosong">✗ Tidak Ada</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($pasien->STATUS_WHATSAPP == 'success'): ?>
                                        <span class="status-badge status-terkirim">✅ Terkirim</span>
                                        <?php if (!empty($pasien->TANGGAL_KIRIM)): ?>
                                            <small style="display: block; color: #666; font-size: 11px;">
                                                <?= date('d/m/Y H:i', strtotime($pasien->TANGGAL_KIRIM)) ?>
                                            </small>
                                        <?php endif; ?>
                                    <?php elseif ($pasien->STATUS_WHATSAPP == 'failed'): ?>
                                        <span class="status-badge status-gagal">❌ Gagal</span>
                                        <?php if (!empty($pasien->NOMOR)): ?>
                                            <a href="https://wa.me/<?= $pasien->NOMOR ?>" target="_blank" class="btn-whatsapp" style="font-size: 11px; margin-top: 2px;">
                                                📱 Kirim Manual
                                            </a>
                                        <?php endif; ?>
                                    <?php elseif (!empty($pasien->NOMOR)): ?>
                                        <span class="status-badge status-belum">⏳ Belum Terkirim</span>
                                        <a href="https://wa.me/<?= $pasien->NOMOR ?>" target="_blank" class="btn-whatsapp" style="font-size: 11px; margin-top: 2px;">
                                            📱 Kirim WA
                                        </a>
                                    <?php else: ?>
                                        <span class="status-badge status-kosong">-</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>

        <div class="actions">
            <?php if (!empty($data) && $statistik['pasien_dengan_nomor'] > 0): ?>
                <a href="<?= base_url('cron_whatsapp/kirim_reminder_h1') ?>?<?= http_build_query(array_filter($filter)) ?>&token=perjanjian_cron_2024" 
                   class="btn btn-success" 
                   onclick="return confirm('Kirim reminder WhatsApp ke <?= $statistik['pasien_dengan_nomor'] ?> pasien?')">
                    📱 Kirim Reminder Sekarang
                </a>
            <?php endif; ?>
            
            <a href="<?= current_url() ?>?<?= http_build_query($_GET) ?>" class="btn btn-primary">
                🔄 Refresh Data
            </a>
            
            <a href="<?= base_url('cron_whatsapp/view_pasien_h1') ?>?format=json&<?= http_build_query(array_filter($filter)) ?>&token=perjanjian_cron_2024" 
               class="btn btn-secondary" target="_blank">
                📄 View JSON
            </a>
        </div>

        <div class="refresh-info">
            <p>📅 Data diambil pada: <?= date('d/m/Y H:i:s') ?></p>
            <p>🔄 Refresh halaman untuk data terbaru</p>
        </div>
    </div>

    <script>
        // Auto refresh setiap 5 menit
        setTimeout(function() {
            location.reload();
        }, 300000);

        // Konfirmasi sebelum kirim reminder
        function confirmSend() {
            return confirm('Apakah Anda yakin ingin mengirim reminder WhatsApp ke semua pasien yang memiliki nomor HP?');
        }
    </script>
</body>
</html>
