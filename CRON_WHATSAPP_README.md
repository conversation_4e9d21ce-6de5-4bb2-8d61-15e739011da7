# Sistem Cron WhatsApp Perjanjian

Sistem otomatis untuk mengirim reminder WhatsApp H-1 kepada pasien yang memiliki perjanjian.

## Fitur Utama

- ✅ Pengiriman reminder H-1 otomatis via cron
- ✅ Filter berdasarkan dokter, r<PERSON><PERSON>, tan<PERSON><PERSON>, dan rencana
- ✅ Logging lengkap untuk tracking pengiriman
- ✅ API endpoint yang aman dengan token
- ✅ Monitoring dan status checking
- ✅ Setup dan testing tools
- ✅ **View pasien H-1 dengan interface web**
- ✅ **Dashboard ringkasan perjanjian**
- ✅ **Statistik real-time per dokter dan ruangan**

## Instalasi

### 1. Setup Database

Jalankan setup untuk membuat tabel logging:

```bash
curl "http://your-domain.com/setup_cron/create_tables?token=setup_cron_perjanjian_2024"
```

### 2. Test Koneksi

Pastikan semua komponen berfungsi:

```bash
curl "http://your-domain.com/setup_cron/test_connection?token=setup_cron_perjanjian_2024"
```

### 3. Test WhatsApp

Test pengiriman WhatsApp:

```bash
curl "http://your-domain.com/setup_cron/test_whatsapp?nomor=081234567890&token=setup_cron_perjanjian_2024"
```

## Penggunaan

### 1. Kirim Reminder H-1 untuk Semua Perjanjian

```bash
# Kirim reminder untuk semua perjanjian besok
curl "http://your-domain.com/cron_whatsapp/kirim_reminder_h1_all?token=perjanjian_cron_2024"

# Kirim reminder untuk tanggal tertentu
curl "http://your-domain.com/cron_whatsapp/kirim_reminder_h1_all?tanggal=2024-12-25&token=perjanjian_cron_2024"

# Kirim dengan pesan custom
curl "http://your-domain.com/cron_whatsapp/kirim_reminder_h1_all?pesan=Jangan%20lupa%20perjanjian%20besok&token=perjanjian_cron_2024"
```

### 2. Kirim Reminder dengan Filter Spesifik

```bash
# Filter berdasarkan dokter
curl "http://your-domain.com/cron_whatsapp/kirim_reminder_h1?dokter=123&token=perjanjian_cron_2024"

# Filter berdasarkan ruangan
curl "http://your-domain.com/cron_whatsapp/kirim_reminder_h1?ruangan=456&token=perjanjian_cron_2024"

# Filter kombinasi dokter dan ruangan
curl "http://your-domain.com/cron_whatsapp/kirim_reminder_h1?dokter=123&ruangan=456&token=perjanjian_cron_2024"

# Filter berdasarkan rencana
curl "http://your-domain.com/cron_whatsapp/kirim_reminder_h1?rencana=789&token=perjanjian_cron_2024"
```

### 3. View Pasien dan Dashboard

```bash
# Lihat daftar pasien H-1 (JSON)
curl "http://your-domain.com/cron_whatsapp/view_pasien_h1?token=perjanjian_cron_2024"

# Lihat daftar pasien dengan filter dokter
curl "http://your-domain.com/cron_whatsapp/view_pasien_h1?dokter=123&token=perjanjian_cron_2024"

# Dashboard ringkasan (JSON)
curl "http://your-domain.com/cron_whatsapp/dashboard?token=perjanjian_cron_2024"
```

**Interface Web (buka di browser):**
```
# Dashboard utama
http://your-domain.com/cron_whatsapp/dashboard?format=html&token=perjanjian_cron_2024

# View detail pasien
http://your-domain.com/cron_whatsapp/view_pasien_h1?format=html&token=perjanjian_cron_2024
```

### 4. Monitoring dan Status

```bash
# Cek status pengiriman hari ini
curl "http://your-domain.com/cron_whatsapp/status?token=perjanjian_cron_2024"

# Cek status berdasarkan tanggal
curl "http://your-domain.com/cron_whatsapp/status?tanggal=2024-12-25&token=perjanjian_cron_2024"

# Cek detail log tertentu
curl "http://your-domain.com/cron_whatsapp/status?log_id=123&token=perjanjian_cron_2024"

# Health check
curl "http://your-domain.com/cron_whatsapp/health"
```

## Setup Cron Jobs

### 1. Reminder Harian (Rekomendasi)

Kirim reminder setiap hari jam 18:00 untuk perjanjian besok:

```bash
# Edit crontab
crontab -e

# Tambahkan baris berikut:
0 18 * * * curl -s "http://your-domain.com/cron_whatsapp/kirim_reminder_h1_all?token=perjanjian_cron_2024" >> /var/log/cron_whatsapp.log 2>&1
```

### 2. Reminder per Dokter

Jika ingin mengirim reminder per dokter dengan jadwal berbeda:

```bash
# Dokter A jam 17:00
0 17 * * * curl -s "http://your-domain.com/cron_whatsapp/kirim_reminder_h1?dokter=123&token=perjanjian_cron_2024" >> /var/log/cron_whatsapp_dokter_a.log 2>&1

# Dokter B jam 18:00
0 18 * * * curl -s "http://your-domain.com/cron_whatsapp/kirim_reminder_h1?dokter=456&token=perjanjian_cron_2024" >> /var/log/cron_whatsapp_dokter_b.log 2>&1
```

### 3. Monitoring Cron

Tambahkan monitoring untuk memastikan cron berjalan:

```bash
# Cek status setiap jam
0 * * * * curl -s "http://your-domain.com/cron_whatsapp/health" >> /var/log/cron_whatsapp_health.log 2>&1
```

## Parameter API

### Endpoint: `/cron_whatsapp/kirim_reminder_h1`

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `dokter` | int | No | ID dokter untuk filter |
| `ruangan` | int | No | ID ruangan untuk filter |
| `tanggal` | string | No | Tanggal perjanjian (YYYY-MM-DD), default: besok |
| `rencana` | int | No | ID rencana untuk filter |
| `pesan` | string | No | Pesan custom, default: reminder standar |
| `token` | string | Yes | Token keamanan: `perjanjian_cron_2024` |

### Endpoint: `/cron_whatsapp/kirim_reminder_h1_all`

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `tanggal` | string | No | Tanggal perjanjian (YYYY-MM-DD), default: besok |
| `pesan` | string | No | Pesan custom, default: reminder standar |
| `token` | string | Yes | Token keamanan: `perjanjian_cron_2024` |

### Endpoint: `/cron_whatsapp/view_pasien_h1` ⭐ **BARU**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `dokter` | int | No | ID dokter untuk filter |
| `ruangan` | int | No | ID ruangan untuk filter |
| `tanggal` | string | No | Tanggal perjanjian (YYYY-MM-DD), default: besok |
| `rencana` | int | No | ID rencana untuk filter |
| `format` | string | No | Format output (json/html), default: json |
| `token` | string | Yes | Token keamanan: `perjanjian_cron_2024` |

### Endpoint: `/cron_whatsapp/dashboard` ⭐ **BARU**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `tanggal` | string | No | Tanggal perjanjian (YYYY-MM-DD), default: besok |
| `format` | string | No | Format output (json/html), default: html |
| `token` | string | Yes | Token keamanan: `perjanjian_cron_2024` |

## Response Format

### Success Response

```json
{
    "status": "success",
    "message": "Pengiriman reminder H-1 selesai",
    "data": {
        "total_perjanjian": 25,
        "berhasil": 23,
        "gagal": 2,
        "log_id": 123,
        "detail": [
            {
                "nomr": "123456",
                "nama": "John Doe",
                "nomor": "+6281234567890",
                "status": "sent"
            }
        ]
    }
}
```

### Error Response

```json
{
    "status": "error",
    "message": "Token tidak valid"
}
```

## Logging

Sistem mencatat semua aktivitas dalam tabel:

- `remun_medis.log_cron_whatsapp` - Log utama eksekusi cron
- `remun_medis.log_detail_cron_whatsapp` - Detail pengiriman per pasien

## Keamanan

- Semua endpoint dilindungi dengan token
- Token default: `perjanjian_cron_2024`
- Ganti token di file `Cron_whatsapp.php` untuk production
- Gunakan HTTPS untuk production

## Troubleshooting

### 1. Cron Tidak Berjalan

```bash
# Cek apakah cron service aktif
sudo systemctl status cron

# Cek log cron
sudo tail -f /var/log/cron.log

# Test manual
curl "http://your-domain.com/cron_whatsapp/health"
```

### 2. WhatsApp Tidak Terkirim

```bash
# Test koneksi WhatsApp
curl "http://your-domain.com/setup_cron/test_whatsapp?nomor=081234567890&token=setup_cron_perjanjian_2024"

# Cek log detail
curl "http://your-domain.com/cron_whatsapp/status?token=perjanjian_cron_2024"
```

### 3. Database Error

```bash
# Test koneksi database
curl "http://your-domain.com/setup_cron/test_connection?token=setup_cron_perjanjian_2024"

# Buat ulang tabel jika perlu
curl "http://your-domain.com/setup_cron/create_tables?token=setup_cron_perjanjian_2024"
```

## Best Practices

1. **Jadwal Optimal**: Kirim reminder jam 17:00-19:00 untuk perjanjian besok
2. **Monitoring**: Setup monitoring untuk memastikan cron berjalan
3. **Backup**: Backup log secara berkala
4. **Rate Limiting**: Sistem sudah include delay 0.5 detik antar pengiriman
5. **Error Handling**: Semua error dicatat dalam log untuk debugging

## Informasi Tambahan

Untuk melihat informasi lengkap sistem:

```bash
curl "http://your-domain.com/setup_cron/info"
```

Untuk melihat daftar dokter, ruangan, dan rencana:

```bash
curl "http://your-domain.com/setup_cron/get_references?token=setup_cron_perjanjian_2024"
```
