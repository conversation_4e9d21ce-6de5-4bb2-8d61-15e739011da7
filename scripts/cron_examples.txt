# Contoh Konfigurasi Cron untuk WhatsApp Reminder Perjanjian
# ============================================================

# Format cron: minute hour day month dayofweek command
# Ganti http://localhost/perjanjian dengan URL domain Anda

# 1. REMINDER HARIAN UNTUK SEMUA PERJANJIAN
# Kirim reminder setiap hari jam 18:00 untuk perjanjian besok
0 18 * * * curl -s "http://localhost/perjanjian/cron_whatsapp/kirim_reminder_h1_all?token=perjanjian_cron_2024" >> /var/log/cron_whatsapp/daily_reminder.log 2>&1

# 2. REMINDER DENGAN PESAN CUSTOM
# Kirim reminder dengan pesan khusus
0 17 * * * curl -s "http://localhost/perjanjian/cron_whatsapp/kirim_reminder_h1_all?pesan=Reminder%3A%20Jangan%20lupa%20perjanjian%20besok%20ya&token=perjanjian_cron_2024" >> /var/log/cron_whatsapp/custom_reminder.log 2>&1

# 3. REMINDER PER DOKTER TERTENTU
# Dokter dengan ID 123 - jam 17:00
0 17 * * * curl -s "http://localhost/perjanjian/cron_whatsapp/kirim_reminder_h1?dokter=123&token=perjanjian_cron_2024" >> /var/log/cron_whatsapp/dokter_123.log 2>&1

# Dokter dengan ID 456 - jam 18:00
0 18 * * * curl -s "http://localhost/perjanjian/cron_whatsapp/kirim_reminder_h1?dokter=456&token=perjanjian_cron_2024" >> /var/log/cron_whatsapp/dokter_456.log 2>&1

# 4. REMINDER PER RUANGAN TERTENTU
# Ruangan dengan ID 789 - jam 16:30
30 16 * * * curl -s "http://localhost/perjanjian/cron_whatsapp/kirim_reminder_h1?ruangan=789&token=perjanjian_cron_2024" >> /var/log/cron_whatsapp/ruangan_789.log 2>&1

# 5. REMINDER KOMBINASI DOKTER DAN RUANGAN
# Dokter 123 di ruangan 456 - jam 17:30
30 17 * * * curl -s "http://localhost/perjanjian/cron_whatsapp/kirim_reminder_h1?dokter=123&ruangan=456&token=perjanjian_cron_2024" >> /var/log/cron_whatsapp/dokter_123_ruangan_456.log 2>&1

# 6. REMINDER BERDASARKAN RENCANA
# Rencana operasi (ID 1) - jam 16:00
0 16 * * * curl -s "http://localhost/perjanjian/cron_whatsapp/kirim_reminder_h1?rencana=1&token=perjanjian_cron_2024" >> /var/log/cron_whatsapp/operasi.log 2>&1

# Rencana konsultasi (ID 2) - jam 18:00
0 18 * * * curl -s "http://localhost/perjanjian/cron_whatsapp/kirim_reminder_h1?rencana=2&token=perjanjian_cron_2024" >> /var/log/cron_whatsapp/konsultasi.log 2>&1

# 7. REMINDER UNTUK HARI KERJA SAJA (Senin-Jumat)
# Hanya kirim reminder di hari kerja
0 18 * * 1-5 curl -s "http://localhost/perjanjian/cron_whatsapp/kirim_reminder_h1_all?token=perjanjian_cron_2024" >> /var/log/cron_whatsapp/weekday_reminder.log 2>&1

# 8. REMINDER MULTIPLE WAKTU
# Kirim reminder 2 kali: sore dan malam
0 17 * * * curl -s "http://localhost/perjanjian/cron_whatsapp/kirim_reminder_h1_all?pesan=Reminder%20sore%3A%20Perjanjian%20besok&token=perjanjian_cron_2024" >> /var/log/cron_whatsapp/reminder_sore.log 2>&1
0 20 * * * curl -s "http://localhost/perjanjian/cron_whatsapp/kirim_reminder_h1_all?pesan=Reminder%20malam%3A%20Jangan%20lupa%20perjanjian%20besok&token=perjanjian_cron_2024" >> /var/log/cron_whatsapp/reminder_malam.log 2>&1

# 9. MONITORING DAN HEALTH CHECK
# Health check setiap 6 jam
0 */6 * * * curl -s "http://localhost/perjanjian/cron_whatsapp/health" >> /var/log/cron_whatsapp/health_check.log 2>&1

# Status check harian jam 23:00
0 23 * * * curl -s "http://localhost/perjanjian/cron_whatsapp/status?token=perjanjian_cron_2024" >> /var/log/cron_whatsapp/daily_status.log 2>&1

# 10. CLEANUP LOG (OPSIONAL)
# Hapus log lama setiap minggu (lebih dari 30 hari)
0 2 * * 0 find /var/log/cron_whatsapp -name "*.log" -mtime +30 -delete

# 11. BACKUP LOG (OPSIONAL)
# Backup log setiap bulan
0 1 1 * * tar -czf /backup/cron_whatsapp_$(date +\%Y\%m).tar.gz /var/log/cron_whatsapp/

# ============================================================
# CONTOH JADWAL BERDASARKAN KEBUTUHAN RUMAH SAKIT
# ============================================================

# SKENARIO A: Rumah Sakit Kecil
# - Satu reminder untuk semua perjanjian jam 18:00
0 18 * * * curl -s "http://localhost/perjanjian/cron_whatsapp/kirim_reminder_h1_all?token=perjanjian_cron_2024" >> /var/log/cron_whatsapp/daily.log 2>&1

# SKENARIO B: Rumah Sakit Menengah
# - Reminder per departemen dengan waktu berbeda
0 16 * * * curl -s "http://localhost/perjanjian/cron_whatsapp/kirim_reminder_h1?ruangan=101&pesan=Reminder%20Poli%20Umum&token=perjanjian_cron_2024" >> /var/log/cron_whatsapp/poli_umum.log 2>&1
0 17 * * * curl -s "http://localhost/perjanjian/cron_whatsapp/kirim_reminder_h1?ruangan=102&pesan=Reminder%20Poli%20Anak&token=perjanjian_cron_2024" >> /var/log/cron_whatsapp/poli_anak.log 2>&1
0 18 * * * curl -s "http://localhost/perjanjian/cron_whatsapp/kirim_reminder_h1?ruangan=103&pesan=Reminder%20Poli%20Kandungan&token=perjanjian_cron_2024" >> /var/log/cron_whatsapp/poli_kandungan.log 2>&1

# SKENARIO C: Rumah Sakit Besar
# - Reminder berdasarkan jenis layanan dan waktu optimal
# Operasi (reminder lebih awal)
0 15 * * * curl -s "http://localhost/perjanjian/cron_whatsapp/kirim_reminder_h1?rencana=1&pesan=Reminder%20Operasi%3A%20Mohon%20puasa%20mulai%20jam%2024.00&token=perjanjian_cron_2024" >> /var/log/cron_whatsapp/operasi.log 2>&1

# Konsultasi reguler
0 18 * * * curl -s "http://localhost/perjanjian/cron_whatsapp/kirim_reminder_h1?rencana=2&token=perjanjian_cron_2024" >> /var/log/cron_whatsapp/konsultasi.log 2>&1

# Pemeriksaan khusus
0 17 * * * curl -s "http://localhost/perjanjian/cron_whatsapp/kirim_reminder_h1?rencana=3&pesan=Reminder%20Pemeriksaan%3A%20Bawa%20hasil%20lab%20sebelumnya&token=perjanjian_cron_2024" >> /var/log/cron_whatsapp/pemeriksaan.log 2>&1

# ============================================================
# TIPS PENGGUNAAN
# ============================================================

# 1. Untuk mengedit crontab:
#    crontab -e

# 2. Untuk melihat crontab aktif:
#    crontab -l

# 3. Untuk melihat log cron sistem:
#    sudo tail -f /var/log/cron.log

# 4. Untuk test manual sebelum dijadwalkan:
#    curl "http://localhost/perjanjian/cron_whatsapp/kirim_reminder_h1_all?token=perjanjian_cron_2024"

# 5. Untuk monitoring real-time:
#    tail -f /var/log/cron_whatsapp/daily_reminder.log

# 6. Format URL encoding untuk pesan:
#    Spasi = %20
#    : = %3A
#    Contoh: "Jangan lupa" = "Jangan%20lupa"

# ============================================================
# FITUR VIEW PASIEN & DASHBOARD (BARU!)
# ============================================================

# Lihat daftar pasien H-1 (JSON)
curl "http://localhost/perjanjian/cron_whatsapp/view_pasien_h1?token=perjanjian_cron_2024"

# Lihat pasien dengan filter dokter
curl "http://localhost/perjanjian/cron_whatsapp/view_pasien_h1?dokter=123&token=perjanjian_cron_2024"

# Lihat pasien dengan filter ruangan dan tanggal
curl "http://localhost/perjanjian/cron_whatsapp/view_pasien_h1?ruangan=456&tanggal=2024-12-25&token=perjanjian_cron_2024"

# Dashboard ringkasan (JSON)
curl "http://localhost/perjanjian/cron_whatsapp/dashboard?token=perjanjian_cron_2024"

# Dashboard untuk tanggal tertentu
curl "http://localhost/perjanjian/cron_whatsapp/dashboard?tanggal=2024-12-25&token=perjanjian_cron_2024"

# ============================================================
# INTERFACE WEB (Buka di Browser)
# ============================================================

# Dashboard utama (HTML)
# http://localhost/perjanjian/cron_whatsapp/dashboard?format=html&token=perjanjian_cron_2024

# View detail pasien (HTML)
# http://localhost/perjanjian/cron_whatsapp/view_pasien_h1?format=html&token=perjanjian_cron_2024

# View pasien dengan filter (HTML)
# http://localhost/perjanjian/cron_whatsapp/view_pasien_h1?format=html&dokter=123&ruangan=456&token=perjanjian_cron_2024

# ============================================================
# TROUBLESHOOTING
# ============================================================

# Jika cron tidak jalan, cek:
# 1. Service cron aktif: sudo systemctl status cron
# 2. Syntax crontab benar: crontab -l
# 3. URL dapat diakses: curl "http://localhost/perjanjian/cron_whatsapp/health"
# 4. Permission log directory: ls -la /var/log/cron_whatsapp/
# 5. Token benar dan tidak expired

# Untuk debug, tambahkan output verbose:
# 0 18 * * * curl -v "http://localhost/perjanjian/cron_whatsapp/kirim_reminder_h1_all?token=perjanjian_cron_2024" >> /var/log/cron_whatsapp/debug.log 2>&1
