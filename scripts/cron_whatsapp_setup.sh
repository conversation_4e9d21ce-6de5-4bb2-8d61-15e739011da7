#!/bin/bash

# Script Setup Cron WhatsApp Perjanjian
# Gunakan: ./cron_whatsapp_setup.sh [command] [options]

# Konfigurasi
BASE_URL="http://localhost/perjanjian"  # Ganti dengan URL domain Anda
SETUP_TOKEN="setup_cron_perjanjian_2024"
CRON_TOKEN="perjanjian_cron_2024"
LOG_DIR="/var/log/cron_whatsapp"

# Warna untuk output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fungsi helper
print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

# Fungsi untuk membuat direktori log
create_log_dir() {
    if [ ! -d "$LOG_DIR" ]; then
        sudo mkdir -p "$LOG_DIR"
        sudo chmod 755 "$LOG_DIR"
        print_success "Direktori log dibuat: $LOG_DIR"
    else
        print_info "Direktori log sudah ada: $LOG_DIR"
    fi
}

# Fungsi setup database
setup_database() {
    print_info "Membuat tabel database..."
    
    response=$(curl -s "$BASE_URL/setup_cron/create_tables?token=$SETUP_TOKEN")
    status=$(echo "$response" | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
    
    if [ "$status" = "success" ]; then
        print_success "Tabel database berhasil dibuat"
    else
        print_error "Gagal membuat tabel database"
        echo "$response"
        exit 1
    fi
}

# Fungsi test koneksi
test_connection() {
    print_info "Testing koneksi sistem..."
    
    response=$(curl -s "$BASE_URL/setup_cron/test_connection?token=$SETUP_TOKEN")
    echo "$response" | python3 -m json.tool 2>/dev/null || echo "$response"
}

# Fungsi test WhatsApp
test_whatsapp() {
    if [ -z "$1" ]; then
        print_error "Nomor telepon diperlukan untuk test WhatsApp"
        echo "Gunakan: $0 test-wa 081234567890"
        exit 1
    fi
    
    print_info "Testing pengiriman WhatsApp ke $1..."
    
    response=$(curl -s "$BASE_URL/setup_cron/test_whatsapp?nomor=$1&token=$SETUP_TOKEN")
    echo "$response" | python3 -m json.tool 2>/dev/null || echo "$response"
}

# Fungsi install cron job
install_cron() {
    print_info "Installing cron job untuk reminder harian..."
    
    # Backup crontab existing
    crontab -l > /tmp/crontab_backup_$(date +%Y%m%d_%H%M%S) 2>/dev/null
    
    # Cek apakah cron job sudah ada
    if crontab -l 2>/dev/null | grep -q "cron_whatsapp/kirim_reminder_h1_all"; then
        print_warning "Cron job sudah ada, skip instalasi"
        return
    fi
    
    # Tambah cron job baru
    (crontab -l 2>/dev/null; echo "# Cron WhatsApp Reminder H-1 Perjanjian") | crontab -
    (crontab -l 2>/dev/null; echo "0 18 * * * curl -s \"$BASE_URL/cron_whatsapp/kirim_reminder_h1_all?token=$CRON_TOKEN\" >> $LOG_DIR/daily_reminder.log 2>&1") | crontab -
    (crontab -l 2>/dev/null; echo "0 */6 * * * curl -s \"$BASE_URL/cron_whatsapp/health\" >> $LOG_DIR/health_check.log 2>&1") | crontab -
    
    print_success "Cron job berhasil diinstall"
    print_info "Reminder akan dikirim setiap hari jam 18:00"
    print_info "Health check setiap 6 jam"
}

# Fungsi kirim reminder manual
send_reminder() {
    print_info "Mengirim reminder H-1..."
    
    url="$BASE_URL/cron_whatsapp/kirim_reminder_h1_all?token=$CRON_TOKEN"
    
    # Tambah parameter jika ada
    if [ ! -z "$1" ]; then
        url="$url&tanggal=$1"
    fi
    if [ ! -z "$2" ]; then
        url="$url&pesan=$(echo "$2" | sed 's/ /%20/g')"
    fi
    
    response=$(curl -s "$url")
    echo "$response" | python3 -m json.tool 2>/dev/null || echo "$response"
}

# Fungsi cek status
check_status() {
    print_info "Mengecek status pengiriman..."
    
    url="$BASE_URL/cron_whatsapp/status?token=$CRON_TOKEN"
    
    if [ ! -z "$1" ]; then
        url="$url&tanggal=$1"
    fi
    
    response=$(curl -s "$url")
    echo "$response" | python3 -m json.tool 2>/dev/null || echo "$response"
}

# Fungsi get references
get_references() {
    print_info "Mengambil data referensi..."
    
    response=$(curl -s "$BASE_URL/setup_cron/get_references?token=$SETUP_TOKEN")
    echo "$response" | python3 -m json.tool 2>/dev/null || echo "$response"
}

# Fungsi health check
health_check() {
    print_info "Health check sistem..."
    
    response=$(curl -s "$BASE_URL/cron_whatsapp/health")
    echo "$response" | python3 -m json.tool 2>/dev/null || echo "$response"
}

# Fungsi view pasien
view_pasien() {
    print_info "Melihat daftar pasien H-1..."

    url="$BASE_URL/cron_whatsapp/view_pasien_h1?token=$CRON_TOKEN"

    # Tambah parameter jika ada
    if [ ! -z "$1" ]; then
        url="$url&tanggal=$1"
    fi
    if [ ! -z "$2" ]; then
        url="$url&dokter=$2"
    fi
    if [ ! -z "$3" ]; then
        url="$url&ruangan=$3"
    fi

    response=$(curl -s "$url")
    echo "$response" | python3 -m json.tool 2>/dev/null || echo "$response"
}

# Fungsi dashboard
show_dashboard() {
    print_info "Membuka dashboard..."

    url="$BASE_URL/cron_whatsapp/dashboard?token=$CRON_TOKEN"

    if [ ! -z "$1" ]; then
        url="$url&tanggal=$1"
    fi

    if [ "$2" = "web" ]; then
        url="$url&format=html"
        print_info "Buka URL berikut di browser:"
        echo "$url"
    else
        response=$(curl -s "$url")
        echo "$response" | python3 -m json.tool 2>/dev/null || echo "$response"
    fi
}

# Fungsi show logs
show_logs() {
    if [ ! -d "$LOG_DIR" ]; then
        print_error "Direktori log tidak ditemukan: $LOG_DIR"
        return
    fi

    print_info "Log files di $LOG_DIR:"
    ls -la "$LOG_DIR"

    if [ ! -z "$1" ]; then
        print_info "Isi log $1:"
        tail -20 "$LOG_DIR/$1"
    fi
}

# Fungsi uninstall
uninstall_cron() {
    print_warning "Menghapus cron job..."
    
    # Backup crontab
    crontab -l > /tmp/crontab_backup_uninstall_$(date +%Y%m%d_%H%M%S) 2>/dev/null
    
    # Hapus cron job
    crontab -l 2>/dev/null | grep -v "cron_whatsapp" | crontab -
    
    print_success "Cron job berhasil dihapus"
}

# Fungsi help
show_help() {
    echo "Cron WhatsApp Setup Script"
    echo "=========================="
    echo ""
    echo "Penggunaan: $0 [command] [options]"
    echo ""
    echo "Commands:"
    echo "  setup                    - Setup lengkap (database + cron)"
    echo "  setup-db                 - Setup database saja"
    echo "  test                     - Test koneksi sistem"
    echo "  test-wa [nomor]          - Test WhatsApp ke nomor tertentu"
    echo "  install-cron             - Install cron job"
    echo "  send [tanggal] [pesan]   - Kirim reminder manual"
    echo "  status [tanggal]         - Cek status pengiriman"
    echo "  view [tanggal] [dokter] [ruangan] - Lihat daftar pasien H-1"
    echo "  dashboard [tanggal] [web] - Lihat dashboard (tambah 'web' untuk browser)"
    echo "  references               - Lihat data dokter/ruangan/rencana"
    echo "  health                   - Health check sistem"
    echo "  logs [filename]          - Lihat log files"
    echo "  uninstall                - Hapus cron job"
    echo "  help                     - Tampilkan help ini"
    echo ""
    echo "Contoh:"
    echo "  $0 setup                           # Setup lengkap"
    echo "  $0 test-wa 081234567890           # Test WhatsApp"
    echo "  $0 send 2024-12-25                # Kirim reminder untuk tanggal tertentu"
    echo "  $0 send \"\" \"Jangan lupa datang\"   # Kirim dengan pesan custom"
    echo "  $0 status 2024-12-25              # Cek status tanggal tertentu"
    echo "  $0 logs daily_reminder.log        # Lihat log harian"
    echo ""
    echo "Konfigurasi:"
    echo "  BASE_URL: $BASE_URL"
    echo "  LOG_DIR:  $LOG_DIR"
}

# Main script
case "$1" in
    "setup")
        create_log_dir
        setup_database
        install_cron
        print_success "Setup lengkap selesai!"
        ;;
    "setup-db")
        setup_database
        ;;
    "test")
        test_connection
        ;;
    "test-wa")
        test_whatsapp "$2"
        ;;
    "install-cron")
        create_log_dir
        install_cron
        ;;
    "send")
        send_reminder "$2" "$3"
        ;;
    "status")
        check_status "$2"
        ;;
    "view")
        view_pasien "$2" "$3" "$4"
        ;;
    "dashboard")
        show_dashboard "$2" "$3"
        ;;
    "references")
        get_references
        ;;
    "health")
        health_check
        ;;
    "logs")
        show_logs "$2"
        ;;
    "uninstall")
        uninstall_cron
        ;;
    "help"|"")
        show_help
        ;;
    *)
        print_error "Command tidak dikenal: $1"
        show_help
        exit 1
        ;;
esac
